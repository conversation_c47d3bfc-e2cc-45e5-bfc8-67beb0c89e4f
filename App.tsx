/* eslint-disable react-native/no-inline-styles */
/**
 * Yo<PERSON> - Tatilsepeti Agency Management App
 * https://github.com/svbasi/tts-yoda
 *
 * @format
 */

import React, { useState } from 'react';
import { StatusBar, StyleSheet, useColorScheme, View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import {
  MD3LightTheme as DefaultTheme,
  PaperProvider,
} from 'react-native-paper';

import {
  GoogleSignin,
} from '@react-native-google-signin/google-signin';

import Notification from './src/components/Notification';
import LandingPage from './src/pages/guest/LandingPage';
import LoginPage from './src/pages/guest/LoginPage';
import BottomTabs from './src/navigation/BottomTabs';
import { UserProvider, useUser } from './src/contexts/UserContext';

GoogleSignin.configure({
  webClientId: '************-d4n2u6t5bnpi5it5a6nfehqe71nn8f63.apps.googleusercontent.com', // client ID of type WEB for your server. Required to get the `idToken` on the user object, and for offline access.
  scopes: ['https://www.googleapis.com/auth/drive.readonly'], // what API you want to access on behalf of the user, default is email and profile
  offlineAccess: true, // if you want to access Google API on behalf of the user FROM YOUR SERVER
  hostedDomain: '', // specifies a hosted domain restriction
  forceCodeForRefreshToken: false, // [Android] related to `serverAuthCode`, read the docs link below *.
  accountName: '', // [Android] specifies an account name on the device that should be used
  iosClientId: '************-bed44sji63p6gl98n91n1authvlvt1fl.apps.googleusercontent.com', // [iOS] if you want to specify the client ID of type iOS (otherwise, it is taken from GoogleService-Info.plist)
  googleServicePlistPath: '', // [iOS] if you renamed your GoogleService-Info file, new name here, e.g. "GoogleService-Info-Staging"
  openIdRealm: '', // [iOS] The OpenID2 realm of the home web server. This allows Google to include the user's OpenID Identifier in the OpenID Connect ID token.
  profileImageSize: 120, // [iOS] The desired height (and width) of the profile image. Defaults to 120px
});

// Define the param list for the stack navigator
type RootStackParamList = {
  Landing: undefined;
  Login: undefined;
  Secure: undefined;
};

// Create a stack navigator
const Stack = createNativeStackNavigator<RootStackParamList>();

// Main App Component
function AppContent() {
  const isDarkMode = useColorScheme() === 'dark';
  const [notification, setNotification] = useState<{ visible: boolean; message: string; type: 'error' | 'success' }>({ visible: false, message: '', type: 'error' });

  const showNotification = (message: string, type: 'error' | 'success' = 'error') => {
    setNotification({ visible: true, message, type });
  };

  const hideNotification = React.useCallback(() => {
    setNotification(prev => ({ ...prev, visible: false }));
  }, []);

  return (
    <UserProvider showNotification={showNotification}>
      <SafeAreaProvider>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <View style={styles.container}>
          <AppMain />
          <Notification 
            message={notification.message} 
            type={notification.type} 
            visible={notification.visible} 
            onClose={hideNotification} 
          />
        </View>
      </SafeAreaProvider>
    </UserProvider>
  );
}

function AppMain() {
  const { isLoading, currentPage } = useUser();
  const navigationRef = React.useRef<any>(null);

  // Navigate to the appropriate screen when currentPage changes
  React.useEffect(() => {
    if (navigationRef.current) {
      const navigator = navigationRef.current;
      switch (currentPage) {
        case 'landing':
          navigator.reset({
            index: 0,
            routes: [{ name: 'Landing' }],
          });
          break;
        case 'login':
          navigator.reset({
            index: 0,
            routes: [{ name: 'Login' }],
          });
          break;
        case 'secure':
          navigator.reset({
            index: 0,
            routes: [{ name: 'Secure' }],
          });
          break;
      }
    }
  }, [currentPage]);

  // Show loading screen while checking cached session
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <View style={styles.loadingIndicator} />
        </View>
      </View>
    );
  }

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator
        initialRouteName={currentPage === 'secure' ? 'Secure' : 'Landing'}
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen 
          name="Landing" 
          component={LandingPage} 
          options={{
            // animation: 'slide_from_right', //slide_from_bottom
          }}
        />
        <Stack.Screen 
          name="Login" 
          component={LoginPage} 
          options={{
            // animation: 'slide_from_right',
          }}
        />
        <Stack.Screen 
          name="Secure" 
          component={BottomTabs} 
          options={{
            animation: 'slide_from_bottom',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: 'tomato',
    secondary: 'yellow',
  },
};

function App() {
  return (
    <PaperProvider theme={theme}>
      <AppContent />
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIndicator: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 5,
    borderColor: '#007AFF',
    borderTopColor: 'transparent',
  },
});

export default App;
