# Yoda - Tatilsepeti Acente Yönetim Uygulaması

Yoda, Tatilsepeti acentelerinin satış raporlama, takip ve yönetim süreçlerini kolaylaştırmak için geliştirilmiş bir mobil uygulamadır.

## Yoda - Tatilsepeti Agency Management App

Yoda is a mobile application developed to facilitate the sales reporting, tracking, and management processes of Tatilsepeti agencies.

## ✨ Features / Özellikler

- **Sales Reporting:** View and track sales performance with detailed reports.
- **Agency Tracking:** Monitor the performance and activities of different agencies.
- **Agency Management:** Manage agency information and settings.

- **Satış Raporlama:** Detaylı raporlar ile satış performansını görüntüleyin ve takip edin.
- **Acente Takibi:** Farklı acentelerin performansını ve aktivitelerini izleyin.
- **Acente Yönetimi:** Acente bilgilerini ve ayarlarını yönetin.

## 🎯 Target Audience / Hedef Kitle

This application is intended for use by Tatilsepeti's partner agencies to help them manage their operations and sales effectively.

<PERSON><PERSON> uygulama, Tatilsepeti'nin iş ortağı olan acentelerin operasyonlarını ve satışlarını etkili bir şekilde yönetmelerine yardımcı olmak amacıyla hazırlanmıştır.

## 💻 Tech Stack / Teknoloji

- **React Native:** A framework for building native apps using React.
- **TypeScript:** A typed superset of JavaScript that compiles to plain JavaScript.
- *Other technologies will be added here as the project progresses.*

## 🚀 Getting Started / Başlarken

### Prerequisites / Gereksinimler

Make sure you have completed the [React Native Environment Setup](https://reactnative.dev/docs/set-up-your-environment) guide.

[React Native Geliştirme Ortamı Kurulumu](https://reactnative.dev/docs/set-up-your-environment) rehberini tamamladığınızdan emin olun.

### Installation & Running / Kurulum ve Çalıştırma

1. **Clone the repository:**
   ```sh
   git clone <repository-url>
   ```
2. **Install dependencies:**
   ```sh
   npm install
   # or
   yarn install
   ```
3. **Start Metro:**
    ```sh
    npm start
    # or
    yarn start
    ```
4. **Run on Android or iOS:**
    ```sh
    # For Android
    npm run android
    # or
    yarn android

    # For iOS
    npm run ios
    # or
    yarn ios
    ```

## 📸 Screenshots / Ekran Görüntüleri

*Screenshots of the application will be added here.*

*Uygulamanın ekran görüntüleri buraya eklenecektir.*
