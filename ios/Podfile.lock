PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - fast_float (8.0.0)
  - FBLazyVector (0.81.1)
  - fmt (11.0.2)
  - glog (0.3.5)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - hermes-engine (0.81.1):
    - hermes-engine/Pre-built (= 0.81.1)
  - hermes-engine/Pre-built (0.81.1)
  - HtmlToPdf (1.3.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - op-sqlite (15.0.3):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - PromisesObjC (2.4.0)
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.81.1)
  - RCTRequired (0.81.1)
  - RCTTypeSafety (0.81.1):
    - FBLazyVector (= 0.81.1)
    - RCTRequired (= 0.81.1)
    - React-Core (= 0.81.1)
  - React (0.81.1):
    - React-Core (= 0.81.1)
    - React-Core/DevSupport (= 0.81.1)
    - React-Core/RCTWebSocket (= 0.81.1)
    - React-RCTActionSheet (= 0.81.1)
    - React-RCTAnimation (= 0.81.1)
    - React-RCTBlob (= 0.81.1)
    - React-RCTImage (= 0.81.1)
    - React-RCTLinking (= 0.81.1)
    - React-RCTNetwork (= 0.81.1)
    - React-RCTSettings (= 0.81.1)
    - React-RCTText (= 0.81.1)
    - React-RCTVibration (= 0.81.1)
  - React-callinvoker (0.81.1)
  - React-Core (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.81.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/CoreModulesHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/Default (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/DevSupport (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.81.1)
    - React-Core/RCTWebSocket (= 0.81.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTAnimationHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTBlobHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTImageHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTLinkingHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTNetworkHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTSettingsHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTTextHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTVibrationHeaders (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTWebSocket (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.81.1)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-CoreModules (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety (= 0.81.1)
    - React-Core/CoreModulesHeaders (= 0.81.1)
    - React-jsi (= 0.81.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.81.1)
    - React-runtimeexecutor
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.81.1)
    - React-debug (= 0.81.1)
    - React-jsi (= 0.81.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-logger (= 0.81.1)
    - React-perflogger (= 0.81.1)
    - React-runtimeexecutor
    - React-timing (= 0.81.1)
    - SocketRocket
  - React-debug (0.81.1)
  - React-defaultsnativemodule (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
    - SocketRocket
  - React-domnativemodule (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Fabric
    - React-Fabric/bridging
    - React-FabricComponents
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.81.1)
    - React-Fabric/attributedstring (= 0.81.1)
    - React-Fabric/bridging (= 0.81.1)
    - React-Fabric/componentregistry (= 0.81.1)
    - React-Fabric/componentregistrynative (= 0.81.1)
    - React-Fabric/components (= 0.81.1)
    - React-Fabric/consistency (= 0.81.1)
    - React-Fabric/core (= 0.81.1)
    - React-Fabric/dom (= 0.81.1)
    - React-Fabric/imagemanager (= 0.81.1)
    - React-Fabric/leakchecker (= 0.81.1)
    - React-Fabric/mounting (= 0.81.1)
    - React-Fabric/observers (= 0.81.1)
    - React-Fabric/scheduler (= 0.81.1)
    - React-Fabric/telemetry (= 0.81.1)
    - React-Fabric/templateprocessor (= 0.81.1)
    - React-Fabric/uimanager (= 0.81.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/animations (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/attributedstring (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/bridging (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistry (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistrynative (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.81.1)
    - React-Fabric/components/root (= 0.81.1)
    - React-Fabric/components/scrollview (= 0.81.1)
    - React-Fabric/components/view (= 0.81.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/legacyviewmanagerinterop (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/root (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/scrollview (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/view (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric/consistency (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/core (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/dom (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/imagemanager (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/leakchecker (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/mounting (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.81.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers/events (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/scheduler (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/telemetry (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/templateprocessor (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.81.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager/consistency (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-FabricComponents (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.81.1)
    - React-FabricComponents/textlayoutmanager (= 0.81.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.81.1)
    - React-FabricComponents/components/iostextinput (= 0.81.1)
    - React-FabricComponents/components/modal (= 0.81.1)
    - React-FabricComponents/components/rncore (= 0.81.1)
    - React-FabricComponents/components/safeareaview (= 0.81.1)
    - React-FabricComponents/components/scrollview (= 0.81.1)
    - React-FabricComponents/components/switch (= 0.81.1)
    - React-FabricComponents/components/text (= 0.81.1)
    - React-FabricComponents/components/textinput (= 0.81.1)
    - React-FabricComponents/components/unimplementedview (= 0.81.1)
    - React-FabricComponents/components/virtualview (= 0.81.1)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/iostextinput (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/modal (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/rncore (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/safeareaview (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/scrollview (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/switch (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/text (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/textinput (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/virtualview (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricImage (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired (= 0.81.1)
    - RCTTypeSafety (= 0.81.1)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.81.1)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - SocketRocket
    - Yoga
  - React-featureflags (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-featureflagsnativemodule (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-graphics (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-utils
    - SocketRocket
  - React-hermes (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.81.1)
    - React-jsi
    - React-jsiexecutor (= 0.81.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.1)
    - React-runtimeexecutor
    - SocketRocket
  - React-idlecallbacksnativemodule (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-ImageManager (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
    - SocketRocket
  - React-jserrorhandler (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
    - SocketRocket
  - React-jsi (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsiexecutor (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.81.1)
    - React-jsi (= 0.81.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.1)
    - React-runtimeexecutor
    - SocketRocket
  - React-jsinspector (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-oscompat
    - React-perflogger (= 0.81.1)
    - React-runtimeexecutor
    - SocketRocket
  - React-jsinspectorcdp (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsinspectornetwork (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsinspectorcdp
    - React-performancetimeline
    - React-timing
    - SocketRocket
  - React-jsinspectortracing (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-oscompat
    - React-timing
    - SocketRocket
  - React-jsitooling (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.81.1)
    - React-jsi (= 0.81.1)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-runtimeexecutor
    - SocketRocket
  - React-jsitracing (0.81.1):
    - React-jsi
  - React-logger (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-Mapbuffer (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-microtasksnativemodule (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - react-native-mmkv (3.3.3):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context (5.6.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.6.1)
    - react-native-safe-area-context/fabric (= 5.6.1)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/common (5.6.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/fabric (5.6.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-vector-icons-lucide (12.3.0)
  - react-native-vector-icons-material-design-icons (12.3.0)
  - React-NativeModulesApple (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-featureflags
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-oscompat (0.81.1)
  - React-perflogger (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-performancetimeline (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
    - SocketRocket
  - React-RCTActionSheet (0.81.1):
    - React-Core/RCTActionSheetHeaders (= 0.81.1)
  - React-RCTAnimation (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-featureflags
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTAppDelegate (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon
    - SocketRocket
  - React-RCTBlob (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTFabric (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-RCTFBReactNativeSpec (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec/components (= 0.81.1)
    - ReactCommon
    - SocketRocket
  - React-RCTFBReactNativeSpec/components (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - SocketRocket
    - Yoga
  - React-RCTImage (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTLinking (0.81.1):
    - React-Core/RCTLinkingHeaders (= 0.81.1)
    - React-jsi (= 0.81.1)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.81.1)
  - React-RCTNetwork (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTRuntime (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - SocketRocket
  - React-RCTSettings (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTText (0.81.1):
    - React-Core/RCTTextHeaders (= 0.81.1)
    - Yoga
  - React-RCTVibration (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-rendererconsistency (0.81.1)
  - React-renderercss (0.81.1):
    - React-debug
    - React-utils
  - React-rendererdebug (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-RuntimeApple (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-RuntimeCore (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-runtimeexecutor (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - React-featureflags
    - React-jsi (= 0.81.1)
    - React-utils
    - SocketRocket
  - React-RuntimeHermes (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-utils
    - SocketRocket
  - React-runtimescheduler (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
    - SocketRocket
  - React-timing (0.81.1):
    - React-debug
  - React-utils (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - React-jsi (= 0.81.1)
    - SocketRocket
  - ReactAppDependencyProvider (0.81.1):
    - ReactCodegen
  - ReactCodegen (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - ReactCommon (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - ReactCommon/turbomodule (= 0.81.1)
    - SocketRocket
  - ReactCommon/turbomodule (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.81.1)
    - React-cxxreact (= 0.81.1)
    - React-jsi (= 0.81.1)
    - React-logger (= 0.81.1)
    - React-perflogger (= 0.81.1)
    - ReactCommon/turbomodule/bridging (= 0.81.1)
    - ReactCommon/turbomodule/core (= 0.81.1)
    - SocketRocket
  - ReactCommon/turbomodule/bridging (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.81.1)
    - React-cxxreact (= 0.81.1)
    - React-jsi (= 0.81.1)
    - React-logger (= 0.81.1)
    - React-perflogger (= 0.81.1)
    - SocketRocket
  - ReactCommon/turbomodule/core (0.81.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.81.1)
    - React-cxxreact (= 0.81.1)
    - React-debug (= 0.81.1)
    - React-featureflags (= 0.81.1)
    - React-jsi (= 0.81.1)
    - React-logger (= 0.81.1)
    - React-perflogger (= 0.81.1)
    - React-utils (= 0.81.1)
    - SocketRocket
  - RNCAsyncStorage (2.2.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNCClipboard (1.16.3):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNDeviceInfo (14.1.1):
    - React-Core
  - RNGestureHandler (2.28.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNGoogleSignin (15.0.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - GoogleSignIn (~> 8.0)
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNScreens (4.15.4):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.15.4)
    - SocketRocket
    - Yoga
  - RNScreens/common (4.15.4):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNShare (12.2.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNSVG (15.13.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.13.0)
    - SocketRocket
    - Yoga
  - RNSVG/common (15.13.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNVectorIcons (10.3.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - HtmlToPdf (from `../node_modules/react-native-html-to-pdf`)
  - "op-sqlite (from `../node_modules/@op-engineering/op-sqlite`)"
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectorcdp (from `../node_modules/react-native/ReactCommon/jsinspector-modern/cdp`)
  - React-jsinspectornetwork (from `../node_modules/react-native/ReactCommon/jsinspector-modern/network`)
  - React-jsinspectortracing (from `../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-mmkv (from `../node_modules/react-native-mmkv`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-vector-icons-lucide (from `../node_modules/@react-native-vector-icons/lucide`)"
  - "react-native-vector-icons-material-design-icons (from `../node_modules/@react-native-vector-icons/material-design-icons`)"
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - SocketRocket (~> 0.7.1)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-07-07-RNv0.81.0-e0fc67142ec0763c6b6153ca2bf96df815539782
  HtmlToPdf:
    :path: "../node_modules/react-native-html-to-pdf"
  op-sqlite:
    :path: "../node_modules/@op-engineering/op-sqlite"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectorcdp:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/cdp"
  React-jsinspectornetwork:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/network"
  React-jsinspectortracing:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-mmkv:
    :path: "../node_modules/react-native-mmkv"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-vector-icons-lucide:
    :path: "../node_modules/@react-native-vector-icons/lucide"
  react-native-vector-icons-material-design-icons:
    :path: "../node_modules/@react-native-vector-icons/material-design-icons"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  fast_float: b32c788ed9c6a8c584d114d0047beda9664e7cc6
  FBLazyVector: b8f1312d48447cca7b4abc21ed155db14742bd03
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: 5683914934d5b6e4240e497e0f4a3b42d1854183
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  hermes-engine: 4f8246b1f6d79f625e0d99472d1f3a71da4d28ca
  HtmlToPdf: 5c0649e93aa1c6583f400268eedea00f15e0fa5f
  op-sqlite: 4de773ca1b052aea35cbaa98c60a5fce9fff2500
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 59ec0ac1f2f39672a0c6e6cecdd39383b764646f
  RCTDeprecation: c4b9e2fd0ab200e3af72b013ed6113187c607077
  RCTRequired: e97dd5dafc1db8094e63bc5031e0371f092ae92a
  RCTTypeSafety: 720403058b7c1380c6a3ae5706981d6362962c89
  React: f1486d005993b0af01943af1850d3d4f3b597545
  React-callinvoker: 133f69368c8559e744efa345223625d412f5dfbe
  React-Core: d6d8c1fd33697cec596d33b820456505ee305686
  React-CoreModules: 81ab751a7668ba161440f9623b994e1a6a3019fe
  React-cxxreact: 16f2a2751d0dce8b569f23c1914edc90f655b01b
  React-debug: e01581e1589f329e61c95b332bf7f4969b10564b
  React-defaultsnativemodule: e956b1d8fe15cc79d23061db229bf88170565f2f
  React-domnativemodule: a18b0f7a31b9c75f12fa369baece5542d1265b36
  React-Fabric: c0237a32c3c0dbea2d2b294c8e95605e1dfe2f57
  React-FabricComponents: 65b03884bd5d9f24c79a631d7d26f0fa079bc4aa
  React-FabricImage: de1ea2f2a0b32ad02e5cbb64827d1eec0439cf0d
  React-featureflags: 02de9c35256cc624269b01d670d99e1fd706ea8d
  React-featureflagsnativemodule: 8b84e67edbaa7b9318390c5bd3ae19790a74f356
  React-graphics: 004b40c1b236ea3bb8de6693439bef9797922ba9
  React-hermes: 2179a018b2f86652f6f33ef23efd9e5ac284b247
  React-idlecallbacksnativemodule: f54ea68f984b12e42feed1e7110623b2c38df4d1
  React-ImageManager: 9dd04b7b62bc5397f876ca5fb1b712e700ce390c
  React-jserrorhandler: 2f90bf50fffea1d012e7f3d717c6adf748b1813d
  React-jsi: b27208f8866e53238534f65f304903e4eff25e05
  React-jsiexecutor: 1d3e827797f592c393860dea91aaa6d53c7715e7
  React-jsinspector: bda319277ae779bc476b736fe3a497c6aed304cd
  React-jsinspectorcdp: 69e1736edfd5420037680b7b4557fa748c3c8216
  React-jsinspectornetwork: 7aa707b057c6129b4af59e0c9160436bbab25022
  React-jsinspectortracing: b4a8a328ad2697f9638daa4b7cc054e0303fa47f
  React-jsitooling: a6c7e2829437b28665e97a398b3374d443125e24
  React-jsitracing: d87ae17dd0eef7844e605945da926c5433fe2b51
  React-logger: d27dd2000f520bf891d24f6e141cde34df41f0ee
  React-Mapbuffer: 0746ffab5ac0f49b7c9347338e3d0c1d9dd634c8
  React-microtasksnativemodule: b0fb3f97372df39bda3e657536039f1af227cc29
  react-native-mmkv: 8d47771bdbbe76fb96e003c343a0798bc9f8ab10
  react-native-safe-area-context: 6d8a7b750e496e37bda47c938320bf2c734d441f
  react-native-vector-icons-lucide: 6295108aa1778b577bd6a3391f5abb9e1df4fdc5
  react-native-vector-icons-material-design-icons: c502df5b988ce85d6c7d2b7ee909818315760b82
  React-NativeModulesApple: 9ec9240159974c94886ebbe4caec18e3395f6aef
  React-oscompat: b12c633e9c00f1f99467b1e0e0b8038895dae436
  React-perflogger: ccf4fd2664b00818645e588623c7531a8b32d114
  React-performancetimeline: a866ba759d8e06e9ba174b4421677edcae487baf
  React-RCTActionSheet: 3f741a3712653611a6bfc5abceb8260af9d0b218
  React-RCTAnimation: 2edeebfba175cc2e937e2752209ab605d3c48f21
  React-RCTAppDelegate: e292321e83ee966897244a032216a70930b758d6
  React-RCTBlob: 8dfb24b6dd4a5af45e1e59e2fd925b2df1e44d08
  React-RCTFabric: b25b02a2016f5cb15926a60c77a8d75865aa3558
  React-RCTFBReactNativeSpec: 20338571a1ed853d01da6c68576aa6e8e107b6f6
  React-RCTImage: c7fe8c2f2ae8bad98ab4d944d5d50a889da4b652
  React-RCTLinking: 9ac21ce9f1af914bb01c06af3752db2ec840d0ee
  React-RCTNetwork: 09a5de71d757dbad4b3fe3615839290200b932aa
  React-RCTRuntime: da3f1e0ce088c20350044cdf1efcd7f8d9b9b40c
  React-RCTSettings: fee112652ac7569ea9abe910206e1685f5f9adba
  React-RCTText: 7ee9d0bc16b3a8149f8df6d70c48e724d0db1d89
  React-RCTVibration: 619d613abaeb05f6fbc2b2e5e33f724f05df8eb8
  React-rendererconsistency: a05f6c37f9389c53213d1e28798e441fa6fbdbcd
  React-renderercss: 3decb27a81648fcdee837c59994b51fff5be5a67
  React-rendererdebug: 3b9a92d36932af52e1b473f2a89ea4b05dbdecdf
  React-RuntimeApple: 4e35fb74be4b721c2e1fd6d54ec66456fa7043e9
  React-RuntimeCore: 0fd7ac6e3e9dd20cb47e87c6b9f35832dd445d5e
  React-runtimeexecutor: 7680156c9f3a5a49c688bc33f9ec5ea1b00527f5
  React-RuntimeHermes: 435b7104a3c749af6251353dcb7317a8e53cbd73
  React-runtimescheduler: 8056b916168e446ea44531883928034e62e76a81
  React-timing: 36da85e32e53008ce73f87528818191e7f2508ba
  React-utils: 71e53d55ce778c6e7c7c9db4b1b9d63ef8f55289
  ReactAppDependencyProvider: 448b422f8af1dedf81374eacc90a15439a0ed7f5
  ReactCodegen: 3baedb0c33f963250c866151b825a3c5194b12f1
  ReactCommon: e897f9a1b4afab370cfefaaf5fb3c80371bc3937
  RNCAsyncStorage: 302f2fac014fd450046c120567ca364632da682b
  RNCClipboard: 962296f7af77f6c039b683e21c2e2255af9c05df
  RNDeviceInfo: 8b6fa8379062949dd79a009cf3d6b02a9c03ca59
  RNGestureHandler: 4f7cc97a71d4fe0fcba38c94acdd969f5f17c91c
  RNGoogleSignin: 87502f82bf180fefb16e160149bfe5b380c558e6
  RNScreens: 5c7f22b19ee2e900e5de2c578471aeb153d1e502
  RNShare: 5d5c5158bc67618ed3f8b5cc008171f1c0607cbe
  RNSVG: eca4f6bb09bcaf582c4f082f3989a6b8513b3e93
  RNVectorIcons: 6acc19c833be864e9c70894e101a587fe491150a
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: fa23995c18b65978347b096d0836f4f5093df545

PODFILE CHECKSUM: 6c0df562a094b3c4e7d4512582a4d57429ff8165

COCOAPODS: 1.16.2
