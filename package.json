{"name": "ttsYoda", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@op-engineering/op-sqlite": "^15.0.3", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-google-signin/google-signin": "^15.0.0", "@react-native-vector-icons/lucide": "^12.3.0", "@react-native-vector-icons/material-design-icons": "^12.3.0", "@react-native/new-app-screen": "0.81.1", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/elements": "^2.6.4", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.26", "@react-navigation/stack": "^7.4.8", "@shopify/flash-list": "^2.0.3", "@types/react-native-vector-icons": "^6.4.18", "react": "19.1.0", "react-native": "0.81.1", "react-native-animatable": "^1.4.0", "react-native-device-info": "^14.1.1", "react-native-gesture-handler": "^2.28.0", "react-native-html-to-pdf": "^1.3.0", "react-native-mmkv": "^3.3.3", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.15.4", "react-native-share": "^12.2.0", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.1", "@react-native/eslint-config": "0.81.1", "@react-native/metro-config": "0.81.1", "@react-native/typescript-config": "0.81.1", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg": "^15.13.0", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "engines": {"node": ">=20"}}