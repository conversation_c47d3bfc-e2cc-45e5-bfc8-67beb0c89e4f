import React, { useRef } from 'react';
import { Animated, Dimensions, ImageSourcePropType, Image, StyleSheet, View, Text, TouchableOpacity, NativeSyntheticEvent, NativeScrollEvent } from 'react-native';

interface ImageCarouselProps {
  images: Array<{ id: string; source: ImageSourcePropType; title?: string; subtitle?: string }>;
  height?: number;
  borderRadius?: number;
  fullScreen?: boolean;
  onIndexChange?: (index: number) => void;
  showLoginButton?: boolean;
  onLoginPress?: () => void;
}

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const ImageCarousel: React.FC<ImageCarouselProps> = ({ images, height = 300, borderRadius = 16, fullScreen = false, onIndexChange, showLoginButton = false, onLoginPress }) => {
  const scrollX = useRef(new Animated.Value(0)).current;
  const effectiveHeight = fullScreen ? SCREEN_HEIGHT : height;
  const effectiveRadius = fullScreen ? 0 : borderRadius;

  const handleMomentumEnd = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (!onIndexChange) return;
    const offsetX = e.nativeEvent.contentOffset.x;
    const index = Math.round(offsetX / SCREEN_WIDTH);
    onIndexChange(index);
  };

  const renderItem = ({ item, index }: { item: { id: string; source: ImageSourcePropType; title?: string; subtitle?: string }; index: number }) => {
    const inputRange = [(index - 1) * SCREEN_WIDTH, index * SCREEN_WIDTH, (index + 1) * SCREEN_WIDTH];
    const scale = scrollX.interpolate({ inputRange, outputRange: [0.98, 1, 0.98], extrapolate: 'clamp' });
    const opacity = scrollX.interpolate({ inputRange, outputRange: [0.9, 1, 0.9], extrapolate: 'clamp' });

    return (
      <View style={{ width: SCREEN_WIDTH, alignItems: 'center' }}>
        <Animated.View style={[styles.card, { height: effectiveHeight, borderRadius: effectiveRadius, transform: [{ scale }], opacity, width: fullScreen ? SCREEN_WIDTH : SCREEN_WIDTH - 40 }]}> 
          <Image source={item.source} style={[styles.image, { height: effectiveHeight, borderRadius: effectiveRadius, width: fullScreen ? SCREEN_WIDTH : SCREEN_WIDTH - 40 }]} resizeMode={fullScreen ? 'cover' : 'cover'} />
          {item.title && (
            <View style={styles.textContainer}>
              <Text style={styles.title}>{item.title}</Text>
              {item.subtitle && <Text style={styles.subtitle}>{item.subtitle}</Text>}
            </View>
          )}
        </Animated.View>
      </View>
    );
  };

  return (
    <View style={{ height: effectiveHeight }}>
      <Animated.FlatList
        data={images}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        horizontal
        showsHorizontalScrollIndicator={false}
        pagingEnabled
        snapToAlignment="center"
        decelerationRate="fast"
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], { useNativeDriver: true })}
        onMomentumScrollEnd={handleMomentumEnd}
        scrollEventThrottle={16}
      />
      {showLoginButton && onLoginPress && (
        <View style={styles.loginButtonContainer}>
          <TouchableOpacity style={styles.loginButton} onPress={onLoginPress}>
            <Text style={styles.loginButtonText}>Login</Text>
          </TouchableOpacity>
        </View>
      )}
      <View style={[styles.pagination, fullScreen && { bottom: 28 }]}>
        {images.map((_, i) => {
          const inputRange = [(i - 1) * SCREEN_WIDTH, i * SCREEN_WIDTH, (i + 1) * SCREEN_WIDTH];
          const dotOpacity = scrollX.interpolate({ inputRange, outputRange: [0.3, 1, 0.3], extrapolate: 'clamp' });
          const dotScale = scrollX.interpolate({ inputRange, outputRange: [1, 1.25, 1], extrapolate: 'clamp' });
          return <Animated.View key={i} style={[styles.dot, { opacity: dotOpacity, transform: [{ scale: dotScale }] }]} />;
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    width: SCREEN_WIDTH - 40,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
  },
  textContainer: {
    position: 'absolute',
    bottom: 120,
    left: 20,
    right: 20,
    alignItems: 'flex-start',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'left',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    textAlign: 'left',
    marginTop: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.95)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  pagination: {
    position: 'absolute',
    bottom: 5,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 6,
    height: 4,
    marginHorizontal: 4,
    borderRadius: 4,
    backgroundColor: '#64B5F6',
  },
  loginButtonContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
  },
  loginButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: '#26A69A',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ImageCarousel;
