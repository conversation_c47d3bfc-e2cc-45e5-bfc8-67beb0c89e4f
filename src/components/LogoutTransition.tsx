import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import * as Animatable from 'react-native-animatable';

type LogoutTransitionProps = {
  children: React.ReactNode;
  onAnimationComplete: () => void;
};

const LogoutTransition: React.FC<LogoutTransitionProps> = ({ children, onAnimationComplete }) => {
  useEffect(() => {
    // Trigger the animation completion after the slide down animation
    const timer = setTimeout(() => {
      onAnimationComplete();
    }, 500); // Match the slide down animation duration

    return () => clearTimeout(timer);
  }, [onAnimationComplete]);

  return (
    <View style={styles.container}>
      <Animatable.View 
        animation="slideOutDown" 
        duration={500}
        style={StyleSheet.absoluteFill}
        useNativeDriver={true}
      >
        {children}
      </Animatable.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default LogoutTransition;
