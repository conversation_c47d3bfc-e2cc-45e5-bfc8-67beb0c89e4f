import React, { useEffect, useRef } from 'react';
import { Animated, Text, View, StyleSheet, TouchableOpacity } from 'react-native';

type NotificationType = 'error' | 'success';

interface NotificationProps {
  message: string;
  type: NotificationType;
  visible: boolean;
  onClose: () => void;
}

const Notification: React.FC<NotificationProps> = ({ message, type, visible, onClose }) => {
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    if (visible) {
      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      
      // Animate in (slide down + fade in)
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
      
      // Auto close after 4 seconds
      timerRef.current = setTimeout(() => {
        // Animate out (slide up + fade out)
        Animated.parallel([
          Animated.timing(slideAnim, {
            toValue: -100,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          })
        ]).start(onClose);
      }, 4000);
    } else {
      // Animate out (slide up + fade out)
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -100,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start(onClose);
    }
    
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [visible, slideAnim, fadeAnim, onClose]);

  const handleClose = () => {
    // Clear any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    
    // Animate out (slide up + fade out)
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start(onClose);
  };

  if (!visible) return null;

  const getIcon = () => {
    if (type === 'error') {
      return '⚠️';
    }
    return '✅';
  };

  const getBackgroundColor = () => {
    if (type === 'error') {
      return '#FEF2F2'; // Light red background
    }
    return '#F0FDF4'; // Light green background
  };

  const getBorderColor = () => {
    if (type === 'error') {
      return '#FECACA'; // Red border
    }
    return '#BBF7D0'; // Green border
  };

  const getTextColor = () => {
    if (type === 'error') {
      return '#B91C1C'; // Dark red text
    }
    return '#166534'; // Dark green text
  };

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
          opacity: fadeAnim,
          backgroundColor: getBackgroundColor(),
          borderLeftColor: getBorderColor(),
        }
      ]}
    >
      <View style={styles.content}>
        <Text style={[styles.icon, { color: getTextColor() }]}>
          {getIcon()}
        </Text>
        <Text style={[styles.message, { color: getTextColor() }]}>
          {message}
        </Text>
        <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
          <Text style={[styles.closeIcon, { color: getTextColor() }]}>✕</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 60,
    left: 20,
    right: 20,
    zIndex: 1000,
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    fontSize: 20,
    marginRight: 12,
  },
  message: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 20,
  },
  closeButton: {
    padding: 4,
    marginLeft: 12,
  },
  closeIcon: {
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default Notification;
