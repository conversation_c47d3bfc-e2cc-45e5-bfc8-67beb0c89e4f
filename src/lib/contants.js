import {Platform} from 'react-native';

const localMode = true;
const ipAddress = Platform.OS === 'android' ? '********' : '127.0.0.1';  

export const appvars = {
    appname : 'YODA',
    localMode: localMode,
    backendhost: localMode ? ipAddress : 'yoda.subanet.com',
    backendprotocol: localMode ? 'http' : 'https',
    port: localMode ? '3000' : '',
    frontendhost: localMode ? ipAddress : 'yoda.subanet.com',
    frontendprotocol: localMode ? 'http' : 'https',
    frontendport: localMode ? '3000' : '',
    appversion: '0.0.1',
    appbuild: '1', 
    backendUri: localMode ? `http://${ipAddress}:3000` : `https://yoda.subanet.com`,
}

export const appIcons = {
    "home": {
        "code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path d=\"M30.56 8.47a8 8 0 0 0-7-7 64.29 64.29 0 0 0-15.06 0 8 8 0 0 0-7 7 64.29 64.29 0 0 0 0 15.06 8 8 0 0 0 7 7 64.29 64.29 0 0 0 15.06 0 8 8 0 0 0 7-7 64.29 64.29 0 0 0 0-15.06zM7.36 15l1.88-6h13.52l1.88 6zM23 17v11.6c-1 .11-2 .2-3 .26V22a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v6.86c-1-.06-2-.15-3-.26V17zm-9 11.94V22h4v6.94c-1.33.06-2.67.06-4 0zm14.58-5.64A6 6 0 0 1 25 28.1V17h1a1 1 0 0 0 1-1.3l-2.5-8a1 1 0 0 0-1-.7h-15a1 1 0 0 0-1 .7l-2.5 8A1 1 0 0 0 6 17h1v11.1a6 6 0 0 1-3.58-4.8 63.65 63.65 0 0 1 0-14.6A6 6 0 0 1 8.7 3.42a63.65 63.65 0 0 1 14.6 0 6 6 0 0 1 5.28 5.28 63.65 63.65 0 0 1 0 14.6z\" data-name=\"home android app aplication phone\"/></svg>"
    },
    "tool": {
        "code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><g data-name=\"tool android app aplication phone\"><path d=\"M30.56 8.47a8 8 0 0 0-7-7 64.29 64.29 0 0 0-15.06 0 8 8 0 0 0-7 7 64.29 64.29 0 0 0 0 15.06 8 8 0 0 0 7 7 64.29 64.29 0 0 0 15.06 0 8 8 0 0 0 7-7 64.29 64.29 0 0 0 0-15.06zm-2 14.83a6 6 0 0 1-5.28 5.28 63.65 63.65 0 0 1-14.6 0 6 6 0 0 1-5.26-5.28 63.65 63.65 0 0 1 0-14.6A6 6 0 0 1 8.7 3.42a63.65 63.65 0 0 1 14.6 0 6 6 0 0 1 5.28 5.28 63.65 63.65 0 0 1 0 14.6z\"/><path d=\"M13 6H9a2 2 0 0 0-2 2v14a1 1 0 0 0 .29.71l3 3a1 1 0 0 0 1.42 0l3-3A1 1 0 0 0 15 22V8a2 2 0 0 0-2-2zm0 2v2H9V8zm-2 15.59-2-2V12h4v9.59zM23 6h-4a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2zm-4 18V8h4v2h-1a1 1 0 0 0 0 2h1v3h-1a1 1 0 0 0 0 2h1v3h-1a1 1 0 0 0 0 2h1v2z\"/></g></svg>"
    },
    "bayiler": {
        "code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><g data-name=\"market android app aplication phone\"><path d=\"M30.56 8.47a8 8 0 0 0-7-7 64.29 64.29 0 0 0-15.06 0 8 8 0 0 0-7 7 64.29 64.29 0 0 0 0 15.06 8 8 0 0 0 7 7 64.83 64.83 0 0 0 15.06 0 8 8 0 0 0 7-7 64.29 64.29 0 0 0 0-15.06zM8.7 3.42a63.65 63.65 0 0 1 14.6 0 6 6 0 0 1 5.28 5.28c.09.76.14 1.53.2 2.3H26.5A2.5 2.5 0 0 1 24 8.5V7a1 1 0 0 0-2 0v1.5a2.5 2.5 0 0 1-5 0V7a1 1 0 0 0-2 0v1.5a2.5 2.5 0 0 1-5 0V7a1 1 0 0 0-2 0v1.5A2.5 2.5 0 0 1 5.5 11H3.22c.06-.77.11-1.54.2-2.3A6 6 0 0 1 8.7 3.42zm-.7 25V18h6v10.94a62.6 62.6 0 0 1-5.3-.36 5.44 5.44 0 0 1-.7-.15zm20.58-5.12a6 6 0 0 1-5.28 5.28A63.55 63.55 0 0 1 16 29V18a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v9.56a6 6 0 0 1-2.58-4.26A63.24 63.24 0 0 1 3.09 13H5.5A4.46 4.46 0 0 0 9 11.29a4.44 4.44 0 0 0 7 0 4.44 4.44 0 0 0 7 0A4.46 4.46 0 0 0 26.5 13h2.41a63.24 63.24 0 0 1-.33 10.3z\"/><path d=\"M24 16h-3a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h3a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2zm-3 5v-3h3v3z\"/></g></svg>"
    },
    "adaybayiler": {
        "code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><g data-name=\"map android app aplication phone\"><path d=\"M30.56 8.47a8 8 0 0 0-7-7 64.29 64.29 0 0 0-15.06 0 8 8 0 0 0-7 7 64.29 64.29 0 0 0 0 15.06 8 8 0 0 0 7 7 64.29 64.29 0 0 0 15.06 0 8 8 0 0 0 7-7 64.29 64.29 0 0 0 0-15.06zM3.42 23.3a63.65 63.65 0 0 1 0-14.6 6 6 0 0 1 1.07-2.79L14.59 16 4.49 26.09a6 6 0 0 1-1.07-2.79zm19.88 5.28a63.65 63.65 0 0 1-14.6 0 6 6 0 0 1-2.79-1.07L16 17.41l10.09 10.1a6 6 0 0 1-2.79 1.07zm5.28-5.28a6 6 0 0 1-1.07 2.79L5.91 4.49A6 6 0 0 1 8.7 3.42a63.65 63.65 0 0 1 14.6 0 6 6 0 0 1 5.28 5.28 63.65 63.65 0 0 1 0 14.6z\"/><path d=\"M22 6a4 4 0 0 0-4 4c0 1.87 2.65 5.8 3.18 6.57a1 1 0 0 0 1.64 0C23.35 15.8 26 11.87 26 10a4 4 0 0 0-4-4zm0 8.18A13.26 13.26 0 0 1 20 10a2 2 0 0 1 4 0 13.36 13.36 0 0 1-2 4.18z\"/><circle cx=\"22\" cy=\"10\" r=\"1\"/></g></svg>"
    },
    "ayarlar": {
        "code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><g data-name=\"setting android app aplication phone\"><path d=\"M30.56 8.47a8 8 0 0 0-7-7 64.29 64.29 0 0 0-15.06 0 8 8 0 0 0-7 7 64.29 64.29 0 0 0 0 15.06 8 8 0 0 0 7 7 64.29 64.29 0 0 0 15.06 0 8 8 0 0 0 7-7 64.29 64.29 0 0 0 0-15.06zm-2 14.83a6 6 0 0 1-5.28 5.28 63.65 63.65 0 0 1-14.6 0 6 6 0 0 1-5.26-5.28 63.65 63.65 0 0 1 0-14.6A6 6 0 0 1 8.7 3.42a63.65 63.65 0 0 1 14.6 0 6 6 0 0 1 5.28 5.28 63.65 63.65 0 0 1 0 14.6z\"/><path d=\"m25.43 15.12-1.56-.3a7.25 7.25 0 0 0-.25-1l1.22-1a2 2 0 0 0 .27-2.82l-1.28-1.57A2 2 0 0 0 21 8.17l-1.2 1a6.72 6.72 0 0 0-.95-.44V7.14a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2l.06 1.57a9.38 9.38 0 0 0-1 .48l-1.26-1a2 2 0 0 0-2.8.37l-1.19 1.78a2 2 0 0 0 .34 2.8l1.27.94a7.85 7.85 0 0 0-.21 1l-1.54.38a2 2 0 0 0-1.46 2.43l.48 1.94A2 2 0 0 0 8 21.32l1.52-.41a6.66 6.66 0 0 0 .68.8l-.67 1.46a2 2 0 0 0 1 2.65l1.82.83a2 2 0 0 0 2.65-1l.66-1.44h1.03l.71 1.41a2 2 0 0 0 2.69.88l1.79-.9a2 2 0 0 0 .88-2.69L22 21.5a5.86 5.86 0 0 0 .63-.83l1.56.32a2 2 0 0 0 2.36-1.55l.41-2a2 2 0 0 0-1.53-2.32zM24.61 19l-1.56-.33a2 2 0 0 0-2.05.83 6 6 0 0 1-.49.64 2 2 0 0 0-.27 2.2l.76 1.47-1.78.9-1-2.05a1 1 0 0 0-1.07-.54 5.9 5.9 0 0 1-.95.11 6.86 6.86 0 0 1-1-.07 1 1 0 0 0-1 .58l-1 2.09-1.85-.83.65-1.45a2 2 0 0 0-.36-2.19 6.21 6.21 0 0 1-.52-.63A2 2 0 0 0 9.06 19l-1.55.39L7 17.43l1.55-.38a2 2 0 0 0 1.49-1.65 5.27 5.27 0 0 1 .16-.79 2 2 0 0 0-.72-2.09l-1.26-1L9.47 10l1.26 1a2 2 0 0 0 2.21.14 4.84 4.84 0 0 1 .72-.36 2 2 0 0 0 1.2-1.87V7.32h2v1.59a2 2 0 0 0 1.26 1.82 6.16 6.16 0 0 1 .74.33 2 2 0 0 0 2.21-.23l1.22-1 1.28 1.54-1.23 1a2 2 0 0 0-.63 2.12 5.35 5.35 0 0 1 .19.79 2 2 0 0 0 1.56 1.58l1.56.33z\"/><path d=\"M15.92 12a4 4 0 0 0 .08 8h.08a4 4 0 0 0-.16-8zM18 16a2 2 0 1 1-2-2 2 2 0 0 1 2 2z\"/></g></svg>"
    },
    "people": {
        "code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path d=\"M30.56 8.47a8 8 0 0 0-7-7 64.29 64.29 0 0 0-15.06 0 8 8 0 0 0-7 7 64.29 64.29 0 0 0 0 15.06 8 8 0 0 0 7 7 64.29 64.29 0 0 0 15.06 0 8 8 0 0 0 7-7 64.29 64.29 0 0 0 0-15.06zM23 3.4h.3a6 6 0 0 1 5.28 5.3V9H23zM17 29a63.9 63.9 0 0 1-8.3-.39A6 6 0 0 1 7 28.1V24a5 5 0 0 1 10 0zM9 14a3 3 0 1 1 3 3 3 3 0 0 1-3-3zm12 14.78c-.67 0-1.33.1-2 .13V24a7 7 0 0 0-3.78-6.21 5 5 0 1 0-6.44 0A7 7 0 0 0 5 24v2.7a6 6 0 0 1-1.58-3.4 63.65 63.65 0 0 1 0-14.6A6 6 0 0 1 8.7 3.42a61.22 61.22 0 0 1 12.3-.2zm7.58-5.48a6 6 0 0 1-5.28 5.28H23V23h5.6c-.01.1-.01.2-.02.3zm.2-2.3H23v-4h6c0 1.34-.12 2.67-.22 4zM23 15v-4h5.78c.1 1.33.17 2.66.19 4z\" data-name=\"people android app aplication phone\"/></svg>"
    },
    "tesis": {
        "code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 64 64\"><path d=\"m28.558 7.098-.47 2.74a1 1 0 0 0 1.45 1.053L32 9.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.942a1 1 0 0 0-.553-1.705l-2.752-.4L32.897.558a1.04 1.04 0 0 0-1.793 0L29.873 3.05l-2.752.4a1 1 0 0 0-.554 1.705zM42.558 12.098l-.47 2.74a1 1 0 0 0 1.45 1.053L46 14.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.942a1 1 0 0 0-.553-1.705l-2.752-.4-1.231-2.493a1.04 1.04 0 0 0-1.793 0L43.873 8.05l-2.752.4a1 1 0 0 0-.554 1.705zM54.558 19.098l-.47 2.74a1 1 0 0 0 1.45 1.053L58 21.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.941a1 1 0 0 0-.553-1.706l-2.752-.4-1.231-2.493a1.04 1.04 0 0 0-1.793 0l-1.231 2.493-2.752.4a1 1 0 0 0-.554 1.706zM14.558 12.098l-.47 2.74a1 1 0 0 0 1.45 1.053L18 14.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.942a1 1 0 0 0-.553-1.705l-2.752-.4-1.232-2.493a1.04 1.04 0 0 0-1.793 0l-1.23 2.493-2.752.4a1 1 0 0 0-.554 1.705zM2.558 19.098l-.47 2.74a1 1 0 0 0 1.45 1.053L6 21.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.941a1 1 0 0 0-.553-1.706l-2.752-.4-1.232-2.493a1.04 1.04 0 0 0-1.793 0l-1.23 2.493-2.752.4a1 1 0 0 0-.554 1.705zM63 62V29a1 1 0 0 0 0-2H46v35h-2V19a1 1 0 0 0-1-1H21a1 1 0 0 0-1 1v43h-2V27H1a1 1 0 0 0 0 2v33a1 1 0 0 0 0 2h62a1 1 0 0 0 0-2zm-8-29a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm-7-21a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zM10 33a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zM3 33a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm33-33a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm-7-21a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm-7-21a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm14 20v-7h-3v7h-2v-7h-3v7h-2V50a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v12z\" style=\"fill:#1b1b1e\"/><path style=\"fill:#1b1b1e\" d=\"M24 36h2v2h-2zM31 29h2v2h-2zM28 51h8v2h-8zM31 36h2v2h-2zM24 22h2v2h-2zM31 43h2v2h-2zM31 22h2v2h-2zM38 29h2v2h-2zM24 43h2v2h-2zM24 29h2v2h-2zM38 22h2v2h-2zM38 43h2v2h-2zM38 36h2v2h-2zM57 48h2v2h-2zM50 48h2v2h-2zM57 55h2v2h-2zM50 55h2v2h-2zM57 34h2v2h-2zM50 34h2v2h-2zM57 41h2v2h-2zM50 41h2v2h-2zM5 48h2v2H5zM12 48h2v2h-2zM5 55h2v2H5zM12 55h2v2h-2zM5 34h2v2H5zM12 34h2v2h-2zM5 41h2v2H5zM12 41h2v2h-2z\"/></svg>"
    },
    "adaytesis": {
        "code": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 64 64\">\n  \n  <defs>\n    <filter id=\"textShadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\"><feDropShadow dx=\"1\" dy=\"1\" stdDeviation=\"1\" flood-color=\"#000000\" flood-opacity=\"0.5\"/>\n    </filter>\n  </defs>\n\n  \n  <path d=\"m28.558 7.098-.47 2.74a1 1 0 0 0 1.45 1.053L32 9.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.942a1 1 0 0 0-.553-1.705l-2.752-.4L32.897.558a1.04 1.04 0 0 0-1.793 0L29.873 3.05l-2.752.4a1 1 0 0 0-.554 1.705zM42.558 12.098l-.47 2.74a1 1 0 0 0 1.45 1.053L46 14.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.942a1 1 0 0 0-.553-1.705l-2.752-.4-1.231-2.493a1.04 1.04 0 0 0-1.793 0L43.873 8.05l-2.752.4a1 1 0 0 0-.554 1.705zM54.558 19.098l-.47 2.74a1 1 0 0 0 1.45 1.053L58 21.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.941a1 1 0 0 0-.553-1.706l-2.752-.4-1.231-2.493a1.04 1.04 0 0 0-1.793 0l-1.231 2.493-2.752.4a1 1 0 0 0-.554 1.706zM14.558 12.098l-.47 2.74a1 1 0 0 0 1.45 1.053L18 14.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.942a1 1 0 0 0-.553-1.705l-2.752-.4-1.232-2.493a1.04 1.04 0 0 0-1.793 0l-1.23 2.493-2.752.4a1 1 0 0 0-.554 1.705zM2.558 19.098l-.47 2.74a1 1 0 0 0 1.45 1.053L6 21.6l2.461 1.293a1 1 0 0 0 1.452-1.054l-.47-2.74 1.99-1.941a1 1 0 0 0-.553-1.706l-2.752-.4-1.232-2.493a1.04 1.04 0 0 0-1.793 0l-1.23 2.493-2.752.4a1 1 0 0 0-.554 1.705zM63 62V29a1 1 0 0 0 0-2H46v35h-2V19a1 1 0 0 0-1-1H21a1 1 0 0 0-1 1v43h-2V27H1a1 1 0 0 0 0 2v33a1 1 0 0 0 0 2h62a1 1 0 0 0 0-2zm-8-29a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm-7-21a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zM10 33a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zM3 33a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1zm33-33a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm-7-21a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm-7-21a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm0 7a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1zm14 20v-7h-3v7h-2v-7h-3v7h-2V50a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v12z\" style=\"fill:#424242\"/>\n  <path style=\"fill:#424242\" d=\"M24 36h2v2h-2zM31 29h2v2h-2zM28 51h8v2h-8zM31 36h2v2h-2zM24 22h2v2h-2zM31 43h2v2h-2zM31 22h2v2h-2zM38 29h2v2h-2zM24 43h2v2h-2zM24 29h2v2h-2zM38 22h2v2h-2zM38 43h2v2h-2zM38 36h2v2h-2zM57 48h2v2h-2zM50 48h2v2h-2zM57 55h2v2h-2zM50 55h2v2h-2zM57 34h2v2h-2zM50 34h2v2h-2zM57 41h2v2h-2zM50 41h2v2h-2zM5 48h2v2H5zM12 48h2v2h-2zM5 55h2v2H5zM12 55h2v2h-2zM5 34h2v2H5zM12 34h2v2h-2zM5 41h2v2H5zM12 41h2v2h-2z\"/>\n\n  \n  <rect x=\"10\" y=\"28\" width=\"44\" height=\"22\" rx=\"3\" ry=\"3\" fill=\"#FFFFFF\" filter=\"url(#textShadow)\"/>\n\n  \n  <text x=\"32\" y=\"46\" text-anchor=\"middle\" font-size=\"18\" font-weight=\"bold\" fill=\"#FF5722\" font-family=\"Arial, sans-serif\" filter=\"url(#textShadow)\">PRE</text>\n</svg>"
    },
};

export const appModules = {
    bayi: {
        name: 'Dealers',
        description: 'Dealer Management',
        moduleName: 'bayi',
        navigationName: 'DealersHome',
        icon: {
            name: 'refrigerator',
            type: 'font-awesome',
            size: 28,
            color: '#517fa4',
        },
        route: 'Bayi',
    },
    satis: {
        name: 'Aday Bayiler',
        icon: 'shopping-cart',
        route: 'Satis',
    }
}

export const apiurls = {
    login: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/auth/mobile`,
    getdealers: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/entity/dealers/list`,
    getdealer: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/entity/dealers/`,
    getvars: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/tenant/variables/`,
    // signup: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/signup`,
    // verifytoken: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/verifytoken`,
    // changepassword: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/changepassword`,
    // resetpassword: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/resetpassword`,
    // getuserdetails: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/getuserdetails`,
    // updateuserdetails: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/updateuserdetails`,
    // getnotes: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/getnotes`,
    // addnote: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/addnote`,
    // updatenote: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/updatenote`,
    // deletenote: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/deletenote`,
};
