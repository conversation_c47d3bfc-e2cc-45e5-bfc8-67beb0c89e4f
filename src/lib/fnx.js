
export function getUniqueModulesAndTitlesFromUser(user) {
    const uniqueModules = new Map(); // module -> moduleTitle eşlemesi için

    if (!user.positionsAndRoles || !Array.isArray(user.positionsAndRoles)) {
      return []; // <PERSON><PERSON><PERSON>li dö<PERSON>
    }

    user.positionsAndRoles.forEach(position => {
      if (position.permissionDetails && Array.isArray(position.permissionDetails)) {
        position.permissionDetails.forEach(detail => {
          const { module, moduleTitle } = detail;
          if (module && !uniqueModules.has(module)) {
            uniqueModules.set(module, moduleTitle);
          }
        });
      }
    });

    // Map'i obje dizisine çevir
    return Array.from(uniqueModules.entries()).map(([module, moduleTitle]) => ({
      module,
      moduleTitle
    }));
  }
  