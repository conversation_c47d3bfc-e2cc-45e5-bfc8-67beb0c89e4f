import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StyleSheet } from 'react-native';
import HomePage from '../pages/member/HomePage';
import ExplorePage from '../pages/member/ExplorePage';
import ProfileStack from './ProfileStack';
import DealerStack from './DealerStack';
import Lucide from '@react-native-vector-icons/lucide';
import { useUser } from '../contexts/UserContext';


const Tab = createBottomTabNavigator();

const HomeIcon = ({ color }: { color: string }) => (
  <Lucide name="house" size={24} color={color} />
);

const ExploreIcon = ({ color }: { color: string }) => (
  <Lucide name="search" size={24} color={color} />
);

const ProfileIcon = ({ color }: { color: string }) => (
  <Lucide name="refrigerator" size={24} color={color} />
);

const BottomTabs = () => {

  const { userInfo, userModules } = useUser();


  const ModuleComponentMapping = [
    {
      module: "home",
      component: HomePage,
    },
    {
      module: "bayi",
      component: DealerStack,
    },
    {
      module: "search",
      component: ExplorePage,
    },
    {
      module: "profile",
      component: ProfileStack,
    },
  ]

  // Create dynamic tabs based on userModules
  const createDynamicTabs = () => {
    const tabs = [];
    
    // Add user modules first
    if (Array.isArray(userModules) && userModules.length > 0) {
      
      // Sort userModules by menuWeight (ascending), modules without weight go to end
      const sortedModules = [...userModules].sort((a, b) => {
        const weightA = a.menuWeight ?? Infinity;
        const weightB = b.menuWeight ?? Infinity;
        return weightA - weightB;
      });

      sortedModules.forEach((userModule) => {
        const mapping = ModuleComponentMapping.find(m => m.module === userModule.module);
        if (mapping) {
          tabs.push({
            module: userModule.module,
            title: userModule.moduleTitle || userModule.module,
            component: mapping.component,
            icon: {
              tabBarIcon: ({ color }: { color: string }) => (
                <Lucide name={userModule.menuIcon || "circle"} size={24} color={color} />
              ),
            }
          });
        }
      });
    }
    
    // Always add search as default
    const hasSearch = tabs.some(tab => tab.module === "search");
    if (!hasSearch) {
      tabs.push({
        module: "search",
        title: "Search",
        component: ExplorePage,
        icon: {
          tabBarIcon: ExploreIcon,
        }
      });
    }
    
    return tabs;
  };

  
  // const BottomIcons = [btnHome, btnSearch, btnProfile]

  const BottomIcons = createDynamicTabs();

  // console.log('Dynamic tabs', BottomIcons);
  // console.log('BT userModules', userModules)
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#6B4EFF',
        tabBarInactiveTintColor: '#666',
        tabBarStyle: styles.tabBar,
        headerShown: false,
        tabBarShowLabel: false,
        tabBarItemStyle: styles.tabItem,
      }}
    >
      {Array.isArray(BottomIcons) && BottomIcons.length !== 0 && BottomIcons.map((btn, index) => {
        return (
          <Tab.Screen
            key={index.toString() + btn.module}
            name={btn.title}
            component={btn.component}
            options={btn.icon}
      />
        )
      })}
      {/* <Tab.Screen
        name="Profile"
        component={ProfileStack}
        options={{
          tabBarIcon: ProfileIcon,
        }}
      /> */}
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: '#ffffff95',
    borderTopWidth: 0,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    height: 60,
    width: 'auto',
    alignSelf: 'center',
    position: 'absolute',
    bottom: 20,
    left: '10%',
    right: '10%',
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 5,
    marginHorizontal: 40,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 0,
    minWidth: 50,
  },
});

export default BottomTabs;
