import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import DealersMain from '../pages/member/dealers/DealersMain.tsx';
import DealersHome from '../pages/member/dealers/DealersHome.tsx';
import DealersList from '../pages/member/dealers/DealersList.tsx';
import DealersDetail from '../pages/member/dealers/DealersDetail.tsx';

type ProfileStackParamList = {
  DealersMain: undefined;
  DealersHome: undefined;
  DealersList: undefined;
  DealersDetail: {
    id: string;
    item: {
      id: string;
      tabelaAdi?: string;
      ticariUnvan?: string;
      segment: string;
      durum: string;
      konum: string;
    };
  };
};

const Stack = createStackNavigator<ProfileStackParamList>();

const ProfileStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="DealersMain" component={DealersMain} />
      <Stack.Screen name="DealersHome" component={DealersHome} />
      <Stack.Screen name="DealersList" component={DealersList} />
      <Stack.Screen name="DealersDetail" component={DealersDetail} />
    </Stack.Navigator>
  );
};

export default ProfileStack;
