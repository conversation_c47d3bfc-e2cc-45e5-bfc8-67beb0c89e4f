/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useUser } from '../contexts/UserContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useNavigation } from '@react-navigation/native';
import Lucide from '@react-native-vector-icons/lucide';

const Header = (props) => {
  const navigation = useNavigation();
  const { userInfo } = useUser();
  const insets = useSafeAreaInsets();
  const userInitials = userInfo?.user?.name
    ? userInfo.user.name.split(' ').map(n => n[0]).join('')
    : '?';

  return (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
        {props.showbackbutton && (
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Lucide name="arrow-left" size={24} color="#000" />
          </TouchableOpacity>
        )}
        <Text style={styles.headerTitle}>{props.title || 'Home'}</Text>

      </View>
      {props.showAvatar && (
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>{userInitials}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6B4EFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default Header;
