/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useState, useMemo } from 'react';
import { useUser } from '../../contexts/UserContext';
import ImageCarousel from '../../components/ImageCarousel';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

type RootStackParamList = {
  Landing: undefined;
  Login: undefined;
  Secure: undefined;
};

type Props = NativeStackScreenProps<RootStackParamList, 'Landing'>;

const LandingPage: React.FC<Props> = ({ navigation }) => {
  const { setCurrentPage } = useUser();

  const insets = useSafeAreaInsets();

  const slides = useMemo(() => ([
    { id: '1', source: require('../../../assets/images/photo3.jpg'), title: 'Yoda Travel Analytics', subtitle: 'Designed to simplify  sales reporting, tracking, and management processes.' },
    { id: '2', source: require('../../../assets/images/photo2.jpg'), title: 'Yoda Travel Analytics', subtitle: 'Designed to simplify  sales reporting, tracking, and management processes.' },
    { id: '3', source: require('../../../assets/images/photo1.jpg'), title: 'Yoda Travel Analytics', subtitle: 'Designed to simplify  sales reporting, tracking, and management processes.' },
  ]), []);

  return (
    <>
      <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1, backgroundColor: '#000' }}>
        <ImageCarousel images={slides} fullScreen={true} />
      </View>
      <View style={[styles.container, { bottom: insets.bottom }]}>
        <View style={styles.landingContent}> 
          {/* Get Started Button */}
          <TouchableOpacity
            style={styles.getStartedButton}
            onPress={() => {
              setCurrentPage('login');
              navigation.navigate('Login');
            }}
          >
            <Text style={styles.getStartedButtonText}>Let's Start</Text>
          </TouchableOpacity>
        </View>
      </View>
      {/* <View style={styles.logoContainer}>
        <Image
          source={require('../../../assets/logo.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </View> */}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 25,
    left: 0,
    right: 0,
    zIndex: 2,
    // flex: 1,
    // backgroundColor: '#f5f5f5',
  },
  landingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  logoContainer: {
    marginBottom: 30,
    position: 'absolute',
    top: 45,
    right: 10,
    zIndex: 2,
    // flex: 1,
    // backgroundColor: '#f5f5f5',

  },
  logo: {
    width: 50,
    height: 50,
    opacity: 0.4,
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 40,
  },
  getStartedButton: {
    backgroundColor: '#26C6DA80',
    paddingHorizontal: 40,
    paddingVertical: 18,
    borderRadius: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    width: '80%',
    alignSelf: 'center',
  },
  getStartedButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
});

export default LandingPage;
