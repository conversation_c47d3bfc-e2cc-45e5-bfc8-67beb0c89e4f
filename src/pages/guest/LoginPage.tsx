import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, KeyboardAvoidingView, Platform, ActivityIndicator } from 'react-native';
import { GoogleSignin, GoogleSigninButton, isErrorWithCode, isSuccessResponse, statusCodes } from '@react-native-google-signin/google-signin';
import { useUser } from '../../contexts/UserContext';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { apiurls, appvars } from '../../lib/contants.js';

type RootStackParamList = {
  Landing: undefined;
  Login: undefined;
  Secure: undefined;
};

type Props = NativeStackScreenProps<RootStackParamList, 'Login'>;

const LoginPage: React.FC<Props> = ({ navigation }) => {
  const { handleLoginSuccess, showNotification } = useUser();
  const [isSigningIn, setIsSigningIn] = useState(false);
  const [loadingStage, setLoadingStage] = useState<null | 'google' | 'server' | 'email'>(null);
  const [email, setEmail] = useState('<EMAIL>'); //<EMAIL>
  const [password, setPassword] = useState('Tatil2026!'); //Taner123!
  const [showPassword, setShowPassword] = useState(false);


  const handleEmailLogin = async () => {
    if (isSigningIn) return;
    
    setIsSigningIn(true);
    setLoadingStage('email');
    try {
      const uri = apiurls.login; //appvars.backendprotocol + '://' + appvars.backendhost + ( appvars.port !== '' ? (':' + appvars.port) : '')  +  '/api/auth/mobile';
      // console.log('handleEmailLogin uri', uri, apiurls.login);
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          clientId: '3015492a443ff70540c42329d9912819',
        }),
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      // console.log('handleEmailLogin data', data);
      handleLoginSuccess(data);
    } catch (error: any) {
      if (showNotification) {
        showNotification('Giriş başarısız oldu', 'error');
      }
      console.error('Error:', error);
    } finally {
      setIsSigningIn(false);
      setLoadingStage(null);
    }
  };

  const postGoogleSignIn = async (response: any) => {
    try {
      const uri = apiurls.login;
      // console.log('postGoogleSignIn', uri);
      const res = await fetch(uri, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          idToken: response?.idToken,
          googleUser: response?.user,
          serverAuthCode: response?.serverAuthCode,
          email: response?.user?.email,
          clientId: '3015492a443ff70540c42329d9912819',
          // response
        }),
      });
  
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
  
      const data = await res.json();
      // console.log('backend response', data);
      handleLoginSuccess(data);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const signIn = async () => {
    if (isSigningIn) return;
    
    setIsSigningIn(true);
    setLoadingStage('google');
    try {
      await GoogleSignin.hasPlayServices();
      const response = await GoogleSignin.signIn();
      
      if (isSuccessResponse(response)) {
        console.log({ userInfo: response });
        setLoadingStage('server');
        await postGoogleSignIn(response.data);
        // handleLoginSuccess(response.data);
      } else {
        // sign in was cancelled by user
        if (showNotification) {
          showNotification('Oturum açma iptal edildi', 'error');
        }
      }
    } catch (error: any) {
      if (isErrorWithCode(error)) {
        switch (error.code) {
          case statusCodes.IN_PROGRESS:
            // operation (eg. sign in) already in progress
            if (showNotification) {
              showNotification('Oturum açma işlemi zaten devam ediyor', 'error');
            }
            break;
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            // Android only, play services not available or outdated
            if (showNotification) {
              showNotification('Google Play hizmetleri kullanılamıyor', 'error');
            }
            break;
          default:
            // some other error happened
            if (showNotification) {
              showNotification('Oturum açma başarısız oldu', 'error');
            }
            break;
        }
      } else {
        // an error that's not related to google sign in occurred
        if (showNotification) {
          showNotification('Oturum açma başarısız oldu', 'error');
        }
      }
    } finally {
      setIsSigningIn(false);
      setLoadingStage(null);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}>
      <View style={styles.container}>
        {/* Back Button */}
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.navigate('Landing')}>
          <Text style={styles.backButtonText}>← </Text>
        </TouchableOpacity>
        
        <View style={styles.loginContent}>
          <Text style={styles.loginTitle}>Sign In</Text>
          <Text style={styles.welcomeText}>Hi! Welcome back, you've been missed</Text>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={styles.input}
              placeholder="<EMAIL>"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Password</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.input}
                placeholder="••••••••••••••"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}>
                <Text>{showPassword ? '👁️' : '👁️‍🗨️'}</Text>
              </TouchableOpacity>
            </View>
            <TouchableOpacity onPress={() => {}}>
              <Text style={styles.forgotPassword}>Forgot Password?</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.signInButton, isSigningIn && styles.disabledButton]}
            onPress={handleEmailLogin}
            disabled={isSigningIn}>
            <Text style={styles.signInButtonText}>Sign In</Text>
          </TouchableOpacity>

          <Text style={styles.orText}>Or sign in with</Text>

          <View style={styles.socialButtons}>
            <TouchableOpacity style={styles.socialButton} onPress={signIn}>
              <Text> Google </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.signupContainer}>
            <Text style={styles.signupText}>Don't have an account? </Text>
            <TouchableOpacity onPress={() => {}}>
              <Text style={styles.signupLink}>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Loading Overlay */}
        {loadingStage && (
          <View style={styles.loadingOverlay}>
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#6C3EFF" />
              <Text style={styles.loadingText}>
                {loadingStage === 'google' 
                  ? 'Google authenticating...' 
                  : loadingStage === 'email' 
                  ? 'Authenticating...' 
                  : 'Server authenticating...'}
              </Text>
            </View>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 10,
    padding: 10,
  },
  backButtonText: {
    fontSize: 18,
    color: '#26C6DA',
    fontWeight: '600',
  },
  loginContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingTop: 40,
  },
  loginTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  welcomeText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    alignSelf: 'flex-start',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
  },
  input: {
    width: '100%',
    height: 50,
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#333',
  },
  passwordContainer: {
    position: 'relative',
    width: '100%',
  },
  eyeIcon: {
    position: 'absolute',
    right: 16,
    top: 12,
    padding: 4,
  },
  forgotPassword: {
    color: '#26C6DA',
    fontSize: 14,
    alignSelf: 'flex-end',
    marginTop: 8,
  },
  signInButton: {
    width: '100%',
    height: 56,
    backgroundColor: '#6C3EFF',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#A890FF',
  },
  signInButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  orText: {
    color: '#666',
    fontSize: 14,
    marginVertical: 20,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
    marginBottom: 30,
  },
  socialButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  signupText: {
    color: '#666',
    fontSize: 14,
  },
  signupLink: {
    color: '#6C3EFF',
    fontSize: 14,
    fontWeight: '600',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 30,
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
  },
  loadingText: {
    marginTop: 15,
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
});

export default LoginPage;
