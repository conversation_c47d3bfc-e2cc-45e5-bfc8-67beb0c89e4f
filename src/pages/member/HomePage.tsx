/* eslint-disable react-native/no-inline-styles */
import React, { useState, useRef, useEffect } from 'react';
import { ScrollView, View, StyleSheet, Animated, Easing, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation, NavigationProp } from '@react-navigation/native';

import { getUniqueModulesAndTitlesFromUser } from '../../lib/fnx.js';

type RootStackParamList = {
  Profile: undefined;
  Home: undefined;
  Explore: undefined;
};

type ProfileStackParamList = {
  ProfileMain: undefined;
  DealersHome: undefined;
};

type NavigationProps = NavigationProp<RootStackParamList>;

import Lucide from '@react-native-vector-icons/lucide';
import {
  Card,
  Text,
} from 'react-native-paper';

import { useUser } from '../../contexts/UserContext';
import Header from '../../navigation/Header.tsx';

const HomePage = () => {

  const { userInfo, userModules } = useUser();
  const insets = useSafeAreaInsets();
  const [showStatusBar, setShowStatusBar] = useState(false);
  const animatedValue = useRef(new Animated.Value(-insets.top)).current;

  const [user, setUser] = useState({});

  const handleScroll = (event: any) => {
    const scrollY = event.nativeEvent.contentOffset.y;
    const threshold = insets.top + 32; // 16 padding top + 16 padding bottom
    if (scrollY > threshold && !showStatusBar) {
      setShowStatusBar(true); 
    } else if (scrollY <= threshold && showStatusBar) {
      setShowStatusBar(false);
    }
  };
  useEffect(() => {
    if (showStatusBar) {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 300,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(animatedValue, {
        toValue: -insets.top - 2,
        duration: 300,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }).start();
    }
  }, [showStatusBar]);

  useEffect(() => {
    if (userInfo) {
      setUser(userInfo.user);
    }
    // const userInitials = userInfo?.user?.name
    //   ? userInfo.user.name.split(' ').map(n => n[0]).join('')
    //   : '?';
    console.log('userInitials', userInfo?.user, userModules);
  }, [userInfo]);


  return (

    <View style={styles.container}>
          <Animated.View 
              style={[
                styles.statusBar, 
                { 
                  minHeight: insets.top,
                  transform: [{ translateY: animatedValue }]
                }
              ]}
            >
              <DummyHeader />
            </Animated.View>

      <ScrollView 
        style={styles.scrollView}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
      <Header />
      <View style={styles.content}>
        <UserModules user={user} />
        {/* <Text>Home Screen Content</Text>
        {Array.from({ length: 100 }, (_, i) => (
          <Text key={i}>Hello World {i}</Text>
        ))} */}

      </View>
    </ScrollView></View>
  );
};

interface UserModulesProps {
  user: {
    accessibleModules?: string[];
  };
}

const UserModules: React.FC<UserModulesProps> = (props) => {
  const navigation = useNavigation<NavigationProps>();
 
  let userModules = getUniqueModulesAndTitlesFromUser(props.user);

  // console.log('userModules props', props, getUniqueModulesAndTitlesFromUser(props.user));
  const handleModulePress = (navigationName: string) => {
    // Navigate to Profile tab first
    navigation.navigate('Profile');
    
    // Then navigate to the specific screen in the Profile stack
    setTimeout(() => {
      navigation.navigate('Profile', {
        screen: navigationName,
      } as never);
    }, 0);
  };

  return (
    <>
    <View style={{ marginTop: 14, paddingHorizontal: 14, backgroundColor: '#eee', }}>
      <Text variant='titleLarge'>Modules</Text>
    </View>
    <View style={styles.userModules}>
      {Array.isArray(userModules) && userModules.map((module: object, i: number) => {
        // const module = (appModules as any)[m];
        if (!module?.module) return null;
        
        return (
          <TouchableOpacity 
            key={i} 
            onPress={() => module.module && handleModulePress(module.module)}
            style={{ width: '100%' }}
          >
            <Card>
              <Card.Content style={{ paddingVertical: 5, paddingHorizontal: 5, backgroundColor: '#ffcc00' }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{ width: 25, justifyContent: 'center', alignItems: 'center' }}>
                    <Lucide 
                      name="refrigerator"
                      size={module.icon?.size || 24} 
                      color={module.icon?.color || '#000'} 
                    />
                  </View>
                  <View style={{ marginLeft: 10, paddingRight: 10, }}>
                    <Text variant="titleLarge" style={{ fontSize: 18 }}>{module.moduleTitle}</Text>
                    <Text variant='bodyMedium' style={{ flexShrink: 0, fontSize: 10, color: '#666', fontStyle: 'italic' }}>{module.module}</Text>
                  </View>
                </View>
              </Card.Content>
            </Card>
          </TouchableOpacity>
        )
      })}
      {/* {Array.from({ length: 100 }, (_, iv) => (
          <View key={iv}> </View>
        ))} */}
      <Text>
        {JSON.stringify(props.user)}
      </Text>
    </View>
    </>
  );
}

const DummyHeader = () => {
  return (
    <View style={[{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 0,
      backgroundColor: 'white',
      borderBottomWidth: 0,
      borderBottomColor: '#000',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
    }, { paddingTop: 44, paddingHorizontal: 16 }]}>
      <Text >Custom</Text>
      <View >
        <Text style={styles.avatarText}>?</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#f5f5f5',
    // paddingBottom: 80,
  },
  scrollView: {
    flex: 1,
  },
  userModules: {
    flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: 5,
    padding: 5,
    marginTop: 10,
    paddingHorizontal: 14,
    // borderWidth: 2,
  },
  statusBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    zIndex: 1000,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6B4EFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
  },
});

export default HomePage;
