import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useUser } from '../../contexts/UserContext';
import { useNavigation } from '@react-navigation/native';
import LogoutTransition from '../../components/LogoutTransition';
import Header from '../../navigation/Header.tsx';

const ProfilePage = () => {
  const { userInfo, handleLogout } = useUser();
  const navigation = useNavigation();
  const [showTransition, setShowTransition] = React.useState(false);


  const handleLogoutWithAnimation = () => {
    setShowTransition(true);
  };

  const onTransitionComplete = () => {
    handleLogout(navigation);
  };

  if (showTransition) {
    return (
      <LogoutTransition onAnimationComplete={onTransitionComplete}>
        <View style={[styles.container]}>
          <View style={styles.content}>
            <Text style={styles.name}>{userInfo?.user?.name || ''}</Text>
          </View>
        </View>
      </LogoutTransition>
    );
  }
  
  return (
    <View style={[styles.container]}>
      <Header />
      <View style={styles.content}>
        <Text style={styles.name}>{userInfo?.user?.name || 'Kullanıcı'}</Text>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogoutWithAnimation}
        >
          <Text style={styles.logoutButtonText}>Çıkış Yap</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    paddingBottom: 80,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6B4EFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  logoutButton: {
    backgroundColor: '#FF3B30',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  logoutButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default ProfilePage;
