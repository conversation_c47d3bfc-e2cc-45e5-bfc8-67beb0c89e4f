import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useUser } from '../../contexts/UserContext';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import LogoutTransition from '../../components/LogoutTransition';

type RootStackParamList = {
  Landing: undefined;
  Login: undefined;
  Secure: undefined;
};

type Props = NativeStackScreenProps<RootStackParamList, 'Secure'>;

const SecurePage: React.FC<Props> = ({ navigation }) => {
  const { userInfo, handleLogout } = useUser();
  const [showTransition, setShowTransition] = useState(false);

  // If userInfo.user is null or undefined, logout immediately
  if (!userInfo?.user) {
    handleLogout(navigation);
    return null;
  }

  const handleLogoutWithAnimation = () => {
    setShowTransition(true);
  };

  const onTransitionComplete = () => {
    // Perform logout after animation
    handleLogout(navigation);
  };

  if (showTransition) {
    return (
      <LogoutTransition onAnimationComplete={onTransitionComplete}>
        <View style={styles.container}>
          <View style={styles.secureContent}>
            <Text style={styles.secureTitle}>Oturum Başarılı</Text>
            <Text style={styles.secureDescription}>
              Hoş geldiniz, {userInfo?.user?.name || 'Kullanıcı'}
            </Text>
            
            <TouchableOpacity 
              style={styles.logoutButton}
              onPress={handleLogoutWithAnimation}
            >
              <Text style={styles.logoutButtonText}>Çıkış Yap</Text>
            </TouchableOpacity>
          </View>
        </View>
      </LogoutTransition>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.secureContent}>
        <Text style={styles.secureTitle}>Oturum Başarılı</Text>
        <Text style={styles.secureDescription}>
          Hoş geldiniz, {userInfo?.user?.name || 'Kullanıcı'}
        </Text>
        
        <TouchableOpacity 
          style={styles.logoutButton}
          onPress={handleLogoutWithAnimation}
        >
          <Text style={styles.logoutButtonText}>Çıkış Yap</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  secureContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  secureTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  secureDescription: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
    marginBottom: 40,
  },
  logoutButton: {
    backgroundColor: '#FF3B30',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  logoutButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default SecurePage;
