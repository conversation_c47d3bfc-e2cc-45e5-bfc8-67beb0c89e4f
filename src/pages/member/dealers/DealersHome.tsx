/* eslint-disable react-native/no-inline-styles */
import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Animated, Easing } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Header from '../../../navigation/Header.tsx';

const DealersHome = () => {
  const insets = useSafeAreaInsets();
  const [showStatusBar, setShowStatusBar] = useState(false);
  const animatedValue = useRef(new Animated.Value(-insets.top)).current;

  const handleScroll = (event: any) => {
    const scrollY = event.nativeEvent.contentOffset.y;
    const threshold = insets.top + 32; // 16 padding top + 16 padding bottom
    if (scrollY > threshold && !showStatusBar) {
      setShowStatusBar(true); 
    } else if (scrollY <= threshold && showStatusBar) {
      setShowStatusBar(false);
    }
  };
  useEffect(() => {
    if (showStatusBar) {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 300,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(animatedValue, {
        toValue: -insets.top - 2,
        duration: 300,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }).start();
    }
  }, [showStatusBar]);

  return (
    <View style={styles.container}>
      <Animated.View 
          style={[
            styles.statusBar, 
            { 
              minHeight: insets.top,
              transform: [{ translateY: animatedValue }]
            }
          ]}
        >
          <DummyHeader />
        </Animated.View>
      <ScrollView 
        style={styles.scrollView}
        onScroll={handleScroll}
        scrollEventThrottle={16}
      >
        <Header title="Dealers" />
        <View style={styles.content}>
          <Text>Dealers Screen Content</Text>
          {/* generate 100 hello world text */}
          {Array.from({ length: 100 }, (_, i) => (
            <Text key={i}>Hello World {i}</Text>
          ))}
          <Text>Explore Screen Content</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const DummyHeader = () => {
  return (
    <View style={[{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 0,
      backgroundColor: 'white',
      borderBottomWidth: 1,
      borderBottomColor: '#000',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
    }, { paddingTop: 44, paddingHorizontal: 16 }]}>
      <Text >Custom</Text>
      <View >
        <Text style={styles.avatarText}>?</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#f5f5f5',
    // paddingBottom: 80,
  },
  scrollView: {
    flex: 1,
  },
  statusBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    zIndex: 1000,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#6B4EFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default DealersHome;
