/* eslint-disable react-native/no-inline-styles */
import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, Pressable, TextInput, Animated } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Lucide from '@react-native-vector-icons/lucide';
import { useNavigation } from '@react-navigation/native';

interface MenuItem {
    id: number;
    title: string;
    navTo: string;
    icon: "microscope" | "library-big" | "user-search" | "map-pin-house" | "clipboard-check" | "chart-no-axes-combined";
    color: string;
    navOptions?: any;
}

const menuItems: MenuItem[] = [
    { id: 1, title: 'Adaylar', navTo: '', icon: 'microscope', color: '#FF6B6B' },
    { id: 2, title: 'Acenteler', navTo: 'DealersList', navOptions: { showBackButton: true }, icon: 'library-big', color: '#4ECDC4' },
    { id: 3, title: 'Kontaklar', navTo: '', icon: 'user-search', color: '#45B7D1' },
    { id: 4, title: 'Aktiviteler', navTo: '', icon: 'map-pin-house', color: '#96CEB4' },
    { id: 5, title: 'Aksiyonlar', navTo: '', icon: 'clipboard-check', color: '#45B7D1' },
    { id: 6, title: 'Raporlar', navTo: '', icon: 'chart-no-axes-combined', color: '#FF6B6B' }
];

interface MenuItemProps {
    item: MenuItem;
}

const MenuItemComponent = ({ item }: MenuItemProps) => {
    const navigation = useNavigation();

    const handlePress = () => {
        if (item.navTo && item.navTo !== '') {
            // Navigate to the specified screen
            navigation.navigate(item.navTo, item?.navOptions);
        } else {
            console.log(`Navigate to ${item.title}`);
        }
    };

    return (
        <Pressable
            style={[styles.menuItem, { backgroundColor: item.color + '15' }]}
            onPress={handlePress}
        >
            <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
                <Lucide name={item.icon} size={24} color={item.color} />
            </View>
            <Text style={styles.menuTitle}>{item.title}</Text>
        </Pressable>
    );
};

const DealersMain = () => {
    const insets = useSafeAreaInsets();
    const [searchQuery, setSearchQuery] = useState('');
    const scrollY = useRef(new Animated.Value(0)).current;

    return (
        <View style={[styles.container, { paddingTop: insets.top }]}>
            <Animated.View style={[
                styles.header,
                {
                    transform: [{
                        translateY: scrollY.interpolate({
                            inputRange: [0, 50],
                            outputRange: [0, -50],
                            extrapolate: 'clamp'
                        })
                    }]
                }
            ]}>
                <Text style={styles.headerTitle}>Acenteler</Text>
            </Animated.View>

            <Animated.View style={[
                styles.searchBar,
                {
                    position: 'absolute',
                    top: insets.top + 50,
                    left: 0,
                    right: 0,
                    zIndex: 1,
                    backgroundColor: '#fff',
                    transform: [{
                        translateY: scrollY.interpolate({
                            inputRange: [0, 50],
                            outputRange: [0, -50],
                            extrapolate: 'clamp'
                        })
                    }]
                }
            ]}>
                <View style={styles.searchInputContainer}>
                    <Lucide name="search" size={20} color="#666" style={styles.searchIcon} />
                    <TextInput
                        style={styles.searchInput}
                        placeholder="Arama yakında burada..."
                        placeholderTextColor="#666"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                    />
                    {searchQuery.length > 0 && (
                        <Pressable onPress={() => setSearchQuery('')}>
                            <Lucide name="x" size={20} color="#666" />
                        </Pressable>
                    )}
                </View>
            </Animated.View>

            <Animated.ScrollView
                style={styles.scrollView}
                contentContainerStyle={[styles.contentContainer, { paddingTop: 80 }]}
                showsVerticalScrollIndicator={false}
                onScroll={Animated.event(
                    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                    { useNativeDriver: true }
                )}
                scrollEventThrottle={16}
            >
                <View style={styles.categorySection}>
                    {/* <Text style={styles.sectionTitle}>Categories</Text> */}
                    <View style={styles.menuGrid}>
                        {menuItems.map((item) => (
                            <MenuItemComponent key={item.id} item={item} />
                        ))}
                    </View>
                </View>

                <View style={styles.popularSection}>
                    <View style={styles.sectionHeader}>
                        <Text style={styles.sectionTitle}>Upcoming activities...</Text>
                        <Pressable>
                            <Text style={styles.viewAllButton}>View All</Text>
                        </Pressable>
                    </View>
                    <View style={styles.sectionHeader}>
                        <Text style={styles.sectionTitle}>Latest tasks...</Text>
                        <Pressable>
                            <Text style={styles.viewAllButton}>View All</Text>
                        </Pressable>
                    </View>

                    <View style={[styles.sectionHeader, { backgroundColor: '#fff', flexDirection: 'column' }]}>
                        {/* Add your listing items here */}
                        {Array.from({ length: 100 }, (_, i) => (
                            <Text key={i}>Hello World {i}</Text>
                        ))}
                    </View>

                </View>
            </Animated.ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFFFFF',
    },
    scrollView: {
        flex: 1,
    },
    contentContainer: {
        padding: 6,
    },
    header: {
        paddingHorizontal: 16,
        paddingVertical: 20,
        backgroundColor: '#FFFFFF',
        zIndex: 1,
    },
    headerTitle: {
        fontSize: 28,
        fontWeight: '700',
        color: '#1A1A1A',
    },
    searchBar: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        backgroundColor: '#FFFFFF',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    searchInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F5F5F5',
        borderRadius: 12,
        padding: 12,
    },
    searchIcon: {
        marginRight: 10,
    },
    searchInput: {
        flex: 1,
        fontSize: 16,
        color: '#1A1A1A',
        padding: 0,
    },
    categorySection: {
        marginBottom: 24,
        // borderWidth: 1,
    },
    popularSection: {
        marginBottom: 24,
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
        paddingHorizontal: 16,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: '600',
        color: '#1A1A1A',
        marginBottom: 6,
        paddingHorizontal: 6,
    },
    viewAllButton: {
        fontSize: 14,
        color: '#FF6B6B',
        fontWeight: '600',
    },
    menuGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        padding: 12,
        gap: 12,
        justifyContent: 'space-between',
    },
    menuItem: {
        width: '30%',
        aspectRatio: 1,
        padding: 12,
        borderRadius: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FFFFFF',
    },
    iconContainer: {
        width: 40,
        height: 40,
        borderRadius: 12,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 8,
    },
    menuTitle: {
        fontSize: 12,
        fontWeight: '600',
        color: '#1A1A1A',
        textAlign: 'center',
    }
});

export default DealersMain;