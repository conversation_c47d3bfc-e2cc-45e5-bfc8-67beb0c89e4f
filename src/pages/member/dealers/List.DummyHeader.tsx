/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';

interface DummyHeaderProps {
  navigation: any;
}

const DummyHeader = ({ navigation }: DummyHeaderProps) => {
  return (
    <View style={[{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 0,
      backgroundColor: 'white',
      borderBottomWidth: 0,
      borderBottomColor: '#9f9b9bff',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
    }, { paddingTop: 44, paddingHorizontal: 16, height: 85, paddingVertical: 0, }]}>
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Lucide name="arrow-left" size={24} color="#000" />
        </TouchableOpacity>
        <Text >Acenteler</Text>
      </View>
      <View >
        <Text style={styles.avatarText}>?</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default DummyHeader;
