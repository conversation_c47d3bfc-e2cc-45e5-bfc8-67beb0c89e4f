import React, { useEffect, useState } from 'react';
import { Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';

interface FilterChipsProps { 
  bayiSegmentleri: any;
  dealerSegmentSelected: string;
  setDealerSegmentSelected: (query: string) => void;
}

const FilterChips: React.FC<FilterChipsProps> = ({
  bayiSegmentleri,
  dealerSegmentSelected,
  setDealerSegmentSelected,
}) => {
  // const [selectedChip, setSelectedChipFn] = useState('');

  const [chips, setChips] = useState([
    {
      "labelID": 1,
      "labelCode": "sube",
      "label": "Şube"
    },
    {
      "labelID": 2,
      "labelCode": "bayi",
      "label": "Bayi"
    },
    {
      "labelID": 3,
      "labelCode": "sistem",
      "label": "Sistem Bayi"
    }
  ])
  // const chipsx = ['Tümü', 'Şube', 'Bayi', 'Sistem Bayi'];

  useEffect(() => {
    if (bayiSegmentleri) {
      // console.log('bayiSegmentleri', bayiSegmentleri?.value)
      // Update chips based on bayiSegmentleri value
      // This is a placeholder implementation - adjust based on your actual data structure
      // For example, if bayiSegmentleri.value is an array of chip objects:
      if (Array.isArray(bayiSegmentleri.value)) {
        setChips(bayiSegmentleri.value);
      }
      // Or if you want to merge with existing chips:
      // setChips(prevChips => [...prevChips, ...bayiSegmentleri.value]);
    }
  }, [bayiSegmentleri])


  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.chipsContainer}
      contentContainerStyle={styles.chipsContentContainer}
    >
      <TouchableOpacity
          style={[
            styles.chip,
            dealerSegmentSelected === '' && styles.selectedChip,
          ]}
          onPress={() => setDealerSegmentSelected('')}
        >
          <Text
            style={[
              styles.chipText,
              dealerSegmentSelected === '' && styles.selectedChipText,
            ]}
          >
            {'Tümü'}
          </Text>
        </TouchableOpacity>

      {chips.map((chip) => (
        <TouchableOpacity
          key={chip?.labelCode}
          style={[
            styles.chip,
            dealerSegmentSelected === chip?.labelCode && styles.selectedChip,
          ]}
          onPress={() => setDealerSegmentSelected(chip?.labelCode)}
        >
          <Text
            style={[
              styles.chipText,
              dealerSegmentSelected === chip?.labelCode && styles.selectedChipText,
            ]}
          >
            {chip?.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  chipsContainer: {
    marginTop: 8,
    marginBottom: 16,
  },
  chipsContentContainer: {
    paddingHorizontal: 16,
    gap: 8,
  },
  chip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedChip: {
    backgroundColor: '#6B4EFF',
    borderColor: '#6B4EFF',
  },
  chipText: {
    fontSize: 14,
    color: '#666',
  },
  selectedChipText: {
    color: '#FFFFFF',
  },
});

export default FilterChips;
