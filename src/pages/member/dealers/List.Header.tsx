import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Header from '../../../navigation/Header';
import SearchBar from './List.SearchBar';
import FilterChips from './List.FilterChips';
interface DealersListHeaderProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  loading: boolean;
  dealerCount: number;
  dealers_vars: any;
  dealerSegmentSelected: string;
  setDealerSegmentSelected: (query: string) => void;
}

const DealersListHeader: React.FC<DealersListHeaderProps> = ({
  searchQuery,
  setSearchQuery,
  loading,
  dealerCount,
  dealers_vars,
  dealerSegmentSelected,
  setDealerSegmentSelected,
}) => {
  return (
    <View>
      <Header title="Acenteler" showbackbutton />
      <SearchBar
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />
      <FilterChips 
        bayiSegmentleri={dealers_vars?.data?.bayiSegmentleri} 
        dealerSegmentSelected={dealerSegmentSelected}
        setDealerSegmentSelected={setDealerSegmentSelected}
      />
      {!loading && (
        <View style={styles.content}>
          <Text style={styles.dealerCount}>
            {dealerCount} bayi .
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  dealerCount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginHorizontal: 16,
    marginVertical: 12,
    textAlign: 'left'
  },
});

export default DealersListHeader;