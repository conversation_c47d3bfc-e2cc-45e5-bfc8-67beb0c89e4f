import React, { useRef } from 'react';
import { View, TextInput, StyleSheet, Pressable, TouchableOpacity } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';

interface SearchBarProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ searchQuery, setSearchQuery }) => {
  const textInputRef = useRef<TextInput>(null);

  return (
    <TouchableOpacity 
      activeOpacity={1} 
      onPress={() => textInputRef.current?.blur()}
    >
      <View style={styles.searchInputContainer}>
        <Lucide name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          ref={textInputRef}
          style={styles.searchInput}
          placeholder="Ara..."
          placeholderTextColor="#666"
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoComplete='off'
          autoCapitalize='none'
        />
        {searchQuery && searchQuery.length > 0 && (
          <Pressable onPress={() => setSearchQuery('')} style={styles.clearIconContainer}>
            <Lucide name="x" size={20} color="#666" />
          </Pressable>
        )}
        <TouchableOpacity style={styles.filterIconContainer}>
          <Lucide name="funnel-plus" size={20} color="#666" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginVertical: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1A1A1A',
    padding: 0,
  },
  filterIconContainer: {
    marginLeft: 10,
  },
  clearIconContainer: {
    marginLeft: 10,
  },
});

export default SearchBar;
