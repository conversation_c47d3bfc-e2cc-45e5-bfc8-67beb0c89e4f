import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import RenderContactInfo from './RenderContactInfo';
import RenderLocationInfo from './RenderLocationInfo';
import RenderOtherInfo from './RenderOtherInfo';

interface BusinessInfo {
    segment?: string;
    durum?: string;
    tabelaAdi?: string;
    ticariUnvan?: string;
    iletisim?: any;
    konum?: any;
    diger?: any;
    backoffice?: any;
    created_at?: string;
    updated_at?: string;
}

interface RenderBusinessInfoProps {
    businessInfo?: BusinessInfo;
    activeTab: string;
    setActiveTab: (tab: string) => void;
    copyToClipboard: (text: string, label: string) => void;
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
}

const RenderBusinessInfo: React.FC<RenderBusinessInfoProps> = ({
    businessInfo,
    activeTab,
    setActiveTab,
    copyToClipboard,
    fadeAnim,
    slideAnim
}) => {
    if (!businessInfo) return null;

    return (
        <Animated.View style={[styles.card, { 
            opacity: fadeAnim, 
            transform: [{ translateY: slideAnim }] 
        }]}>
            <View style={styles.cardHeader}>
                <View style={styles.titleContainer}>
                    <Lucide name="briefcase" size={20} color="#6B4EFF" />
                    <Text style={styles.cardTitle}>Acente Bilgileri</Text>
                </View>
                <View style={styles.statusContainer}>
                    {businessInfo.segment && (
                        <Text style={styles.infoValue_s}>{businessInfo.segment}</Text>
                    )}
                    {businessInfo.durum && (
                        <Text style={[
                            styles.infoValue_s, 
                            businessInfo.durum === 'aktif' ? styles.activeStatus : styles.inactiveStatus
                        ]}>
                            {businessInfo.durum}
                        </Text>
                    )}
                </View>
            </View>
            <View>
                <View style={[styles.cardContent, { paddingBottom: 0 }]}>
                    {businessInfo.tabelaAdi && (
                        <View style={styles.infoRow}>
                            <Lucide name="signpost" size={16} color="#666" />
                            <TouchableOpacity onLongPress={() => copyToClipboard(businessInfo.tabelaAdi!, 'Tabela adı')}>
                                <Text style={styles.infoValue_t}>{businessInfo.tabelaAdi}</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                    {businessInfo.ticariUnvan && (
                        <View style={styles.infoRow}>
                            <TouchableOpacity onLongPress={() => copyToClipboard(businessInfo.ticariUnvan!, 'Ticari ünvan')}>
                                <Text style={styles.infoValue_t}>{businessInfo.ticariUnvan}</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
                <View style={styles.tabContainer}>
                    <TouchableOpacity
                        style={[styles.tab, activeTab === 'iletisim' && styles.activeTab]}
                        onPress={() => setActiveTab('iletisim')}>
                        <Text style={[styles.tabText, activeTab === 'iletisim' && styles.activeTabText]}>
                            İletişim
                        </Text>
                    </TouchableOpacity>
                    {/* Other tabs remain */}
                </View>
            </View>
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    titleContainer: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: 2,
    },
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
        marginLeft: 'auto',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    infoValue_t: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'left',
        marginLeft: 5,
    },
    infoValue_s: {
        fontSize: 12,
        color: '#333',
        fontWeight: '500',
        textAlign: 'right',
    },
    activeStatus: {
        color: '#4CAF50',
        fontWeight: '600',
    },
    inactiveStatus: {
        color: '#FF6B6B',
        fontWeight: '600',
    },
    tabContainer: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
        marginHorizontal: 16,
    },
    tab: {
        flex: 1,
        paddingVertical: 8,
        alignItems: 'center',
        borderBottomWidth: 2,
        borderBottomColor: 'transparent',
    },
    activeTab: {
        borderBottomColor: '#6B4EFF',
    },
    tabText: {
        fontSize: 11,
        color: '#666',
        fontWeight: '500',
    },
    activeTabText: {
        color: '#6B4EFF',
        fontWeight: '600',
    },
});

export default RenderBusinessInfo;