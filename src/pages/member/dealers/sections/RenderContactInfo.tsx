import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';

interface ContactInfo {
    cep?: string;
    ofis?: string;
    email?: string;
    webSitesi?: string;
    instagram?: string;
}

interface RenderContactInfoProps {
    iletisim?: ContactInfo;
    copyToClipboard: (text: string, label: string) => void;
}

const RenderContactInfo: React.FC<RenderContactInfoProps> = ({ iletisim, copyToClipboard }) => {
    if (!iletisim) return null;

    return (
        <View style={styles.tabContent}>
            {(iletisim.ofis || iletisim.cep) && (
                <View style={styles.infoRow}>
                    <Lucide name="phone" size={16} color="#666" />
                    <Text style={styles.infoLabel}>Telefon:</Text>
                    {iletisim.ofis && (
                        <TouchableOpacity onLongPress={() => copyToClipboard(iletisim.ofis!, 'Ofis telefonu')}>
                            <Text style={styles.infoValue}>{iletisim.ofis}</Text>
                        </TouchableOpacity>
                    )}
                    <Text>&nbsp;&nbsp;</Text>
                    {iletisim.cep && (
                        <TouchableOpacity onLongPress={() => copyToClipboard(iletisim.cep!, 'Cep telefonu')}>
                            <Text style={styles.infoValue}>{iletisim.cep}</Text>
                        </TouchableOpacity>
                    )}
                </View>
            )}
            {iletisim.email && (
                <View style={styles.infoRow}>
                    <Lucide name="mail" size={16} color="#666" />
                    <Text style={styles.infoLabel}>E-posta:</Text>
                    <TouchableOpacity onLongPress={() => copyToClipboard(iletisim.email!, 'E-posta')}>
                        <Text style={styles.infoValue}>{iletisim.email}</Text>
                    </TouchableOpacity>
                </View>
            )}
            {iletisim.webSitesi && (
                <View style={styles.infoRow}>
                    <Lucide name="globe" size={16} color="#666" />
                    <Text style={styles.infoLabel}>Web Sitesi:</Text>
                    <TouchableOpacity onLongPress={() => copyToClipboard(iletisim.webSitesi!, 'Web sitesi')}>
                        <Text style={styles.infoValue}>{iletisim.webSitesi}</Text>
                    </TouchableOpacity>
                </View>
            )}
            {iletisim.instagram && (
                <View style={[styles.infoRow, {marginBottom: 0}]}>
                    <View style={styles.instagramContainer}>
                        <Lucide name="instagram" size={16} color="#666" />
                        <Text numberOfLines={1} style={styles.infoLabel}>Instagram:</Text>
                    </View>
                    <TouchableOpacity style={styles.instagramTouchable} 
                        onLongPress={() => copyToClipboard(iletisim.instagram!, 'Instagram')}>
                        <Text numberOfLines={2} style={styles.instagramText}>
                            {iletisim.instagram}
                        </Text>
                    </TouchableOpacity>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    tabContent: {
        paddingVertical: 8,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        color: '#666',
        marginLeft: 12,
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'right',
    },
    instagramContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flexShrink: 1,
        gap: 1,
        borderWidth: 0,
        minWidth: 100
    },
    instagramTouchable: {
        flex: 1
    },
    instagramText: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flexGrow: 1,
        textAlign: 'right',
    }
});

export default RenderContactInfo;