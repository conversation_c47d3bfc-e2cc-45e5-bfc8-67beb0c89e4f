/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Animated,
    TouchableWithoutFeedback,
    KeyboardAvoidingView,
    ScrollView,
    Platform,
    Modal
} from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import { TextInput, Checkbox, Button } from 'react-native-paper';

interface RenderContactPeopleProps {
    createdAt?: string;
    updatedAt?: string;
    copyToClipboard: (text: string, label: string) => void;
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
}

const RenderContactPeople: React.FC<RenderContactPeopleProps> = ({
    createdAt,
    updatedAt,
    copyToClipboard,
    fadeAnim,
    slideAnim
}) => {
    const [modalVisible, setModalVisible] = React.useState(false);
    const [fullName, setFullName] = React.useState('');
    const [position, setPosition] = React.useState('');
    const [phone, setPhone] = React.useState('');
    const [email, setEmail] = React.useState('');
    const [isAuthorized, setIsAuthorized] = React.useState(false);
    const [isSaving, setIsSaving] = React.useState(false);
    
    // Animation values for modal
    const modalScaleAnim = React.useRef(new Animated.Value(0.8)).current;
    const modalOpacityAnim = React.useRef(new Animated.Value(0)).current;

    // Handle modal open with animation
    const openModal = () => {
        // Reset animation values first
        modalScaleAnim.setValue(0.8);
        modalOpacityAnim.setValue(0);

        setModalVisible(true);

        // Start animation after modal is visible
        setTimeout(() => {
            Animated.parallel([
                Animated.timing(modalScaleAnim, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(modalOpacityAnim, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                })
            ]).start();
        }, 50);
    };

    // Handle modal close with animation
    const closeModal = () => {
        Animated.parallel([
            Animated.timing(modalOpacityAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start(() => {
            setModalVisible(false);
            // Reset form fields
            setFullName('');
            setPosition('');
            setPhone('');
            setEmail('');
            setIsAuthorized(false);
            setIsSaving(false);
        });
    };

    const handleSave = async () => {
        // Validate required fields
        if (!fullName || !position || !phone || !email) {
            return;
        }
        
        setIsSaving(true);
        
        // Simulate saving with 2-second delay
        await new Promise((resolve) => setTimeout(() => resolve(null), 2000));
        
        console.log({
            fullName,
            position,
            phone,
            email,
            isAuthorized
        });
        
        // Close modal after saving
        closeModal();
    };

    const handleCancel = () => {
        closeModal();
    };
    
    return (
        <>
            <Animated.View style={[styles.card, {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
            }]}>
                <View style={[styles.cardHeader, { justifyContent: 'space-between', alignItems: 'center' }]}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                        <Lucide name="users" size={20} color="#6B4EFF" />
                        <Text style={styles.cardTitle}>Kontaklar</Text>
                    </View>
                    <View>
                        <TouchableOpacity style={styles.addButton} onPress={openModal}>
                            <Lucide name="plus" size={14} color="#6B4EFF" />
                            <Text style={styles.addButtonText}>Yeni Kontak</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={styles.cardContent}>
                    {createdAt && (
                        <View style={styles.infoRow}>
                            <Lucide name="calendar-plus" size={16} color="#666" />
                            <Text style={styles.infoLabel}>Oluşturulma:</Text>
                            <TouchableOpacity onLongPress={() => copyToClipboard(
                                new Date(createdAt).toLocaleDateString('tr-TR'),
                                'Oluşturulma tarihi'
                            )}>
                                <Text style={styles.infoValue}>
                                    {new Date(createdAt).toLocaleDateString('tr-TR')}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}
                    {updatedAt && (
                        <View style={styles.infoRow}>
                            <Lucide name="refresh-cw" size={16} color="#666" />
                            <Text style={styles.infoLabel}>Güncelleme:</Text>
                            <TouchableOpacity onLongPress={() => copyToClipboard(
                                new Date(updatedAt).toLocaleDateString('tr-TR'),
                                'Güncelleme tarihi'
                            )}>
                                <Text style={styles.infoValue}>
                                    {new Date(updatedAt).toLocaleDateString('tr-TR')}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </Animated.View>

            {/* Modern Contact Form Modal */}
            <Modal
                visible={modalVisible}
                transparent={true}
                animationType="fade"
                onRequestClose={handleCancel}
            >
                <TouchableWithoutFeedback onPress={handleCancel}>
                    <View style={styles.modalOverlay}>
                        <TouchableWithoutFeedback onPress={(e) => e.stopPropagation()}>
                            <KeyboardAvoidingView
                                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                                style={styles.keyboardAvoidingView}
                            >

                        <Animated.View
                            style={[
                                styles.modalContainer,
                                {
                                    opacity: modalOpacityAnim,
                                    transform: [{ scale: modalScaleAnim }]
                                }
                            ]}
                        >
                            {/* Modal Header */}
                            <View style={styles.modalHeader}>
                                <View style={styles.modalHeaderContent}>
                                    <View style={styles.modalIconContainer}>
                                        <Lucide name="user-plus" size={24} color="#6B4EFF" />
                                    </View>
                                    <View style={styles.modalTitleContainer}>
                                        <Text style={styles.modalTitle}>Yeni Kontak Ekle</Text>
                                        <Text style={styles.modalSubtitle}>Bayi için kontak bilgilerini girin</Text>
                                    </View>
                                </View>
                                <TouchableOpacity
                                    style={styles.closeButton}
                                    onPress={handleCancel}
                                    disabled={isSaving}
                                >
                                    <Lucide name="x" size={20} color="#666" />
                                </TouchableOpacity>
                            </View>

                            {/* Modal Content with ScrollView */}
                            <ScrollView
                                style={styles.modalScrollView}
                                showsVerticalScrollIndicator={false}
                                keyboardShouldPersistTaps="handled"
                            >
                                <View style={styles.modalContent}>
                                    {/* Full Name Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Ad Soyad *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="user" size={18} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={fullName}
                                                onChangeText={setFullName}
                                                placeholder="Örn: Ahmet Yılmaz"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* Position Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Pozisyon *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="briefcase" size={18} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={position}
                                                onChangeText={setPosition}
                                                placeholder="Örn: Satış Müdürü"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* Phone Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Telefon *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="phone" size={18} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={phone}
                                                onChangeText={setPhone}
                                                placeholder="Örn: +90 555 123 45 67"
                                                keyboardType="phone-pad"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* Email Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>E-posta *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="mail" size={18} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={email}
                                                onChangeText={setEmail}
                                                placeholder="Örn: <EMAIL>"
                                                keyboardType="email-address"
                                                autoCapitalize="none"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* Authorization Checkbox */}
                                    <TouchableOpacity
                                        style={styles.checkboxContainer}
                                        onPress={() => setIsAuthorized(!isAuthorized)}
                                        disabled={isSaving}
                                    >
                                        <Checkbox
                                            status={isAuthorized ? 'checked' : 'unchecked'}
                                            onPress={() => setIsAuthorized(!isAuthorized)}
                                            color="#6B4EFF"
                                            disabled={isSaving}
                                        />
                                        <Text style={styles.checkboxLabel}>
                                            Yetkili kişi olarak işaretle
                                        </Text>
                                        <Lucide name="shield-check" size={16} color="#6B4EFF" style={styles.checkboxIcon} />
                                    </TouchableOpacity>
                                </View>
                            </ScrollView>

                            {/* Modal Actions */}
                            <View style={styles.modalActions}>
                                <Button
                                    mode="outlined"
                                    onPress={handleCancel}
                                    style={[styles.actionButton, styles.cancelButton]}
                                    labelStyle={styles.cancelButtonText}
                                    disabled={isSaving}
                                >
                                    İptal
                                </Button>
                                <Button
                                    mode="contained"
                                    onPress={handleSave}
                                    style={[styles.actionButton, styles.saveButton]}
                                    labelStyle={styles.saveButtonText}
                                    disabled={isSaving || !fullName || !position || !phone || !email}
                                    loading={isSaving}
                                    icon={isSaving ? undefined : "check"}
                                >
                                    {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                                </Button>
                            </View>
                        </Animated.View>
                            </KeyboardAvoidingView>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>
        </>
    )
};

export default RenderContactPeople;

const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        color: '#666',
        marginLeft: 12,
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'right',
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 20,
        backgroundColor: '#F0EDFF',
    },
    addButtonText: {
        fontSize: 12,
        color: '#6B4EFF',
        fontWeight: '500',
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
    },
    keyboardAvoidingView: {
        justifyContent: 'center',
        alignItems: 'center',
        width: '100%',
    },
    modalContainer: {
        width: '90%',
        maxWidth: 420,
        maxHeight: '85%',
        backgroundColor: 'white',
        borderRadius: 20,
        overflow: 'hidden',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.25,
        shadowRadius: 20,
        elevation: 12,
        zIndex: 3,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 24,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
        backgroundColor: '#FAFBFF',
    },
    modalHeaderContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    modalIconContainer: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: '#F0EDFF',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    modalTitleContainer: {
        flex: 1,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: '700',
        color: '#1A1A1A',
        marginBottom: 4,
    },
    modalSubtitle: {
        fontSize: 14,
        color: '#666',
        fontWeight: '400',
    },
    closeButton: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#F5F5F5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalScrollView: {
        flex: 1,
    },
    modalContent: {
        padding: 24,
        paddingTop: 20,
    },
    inputGroup: {
        marginBottom: 24,
    },
    inputLabel: {
        fontSize: 15,
        fontWeight: '600',
        color: '#1A1A1A',
        marginBottom: 12,
        marginLeft: 4,
    },
    inputContainer: {
        position: 'relative',
    },
    inputIcon: {
        position: 'absolute',
        left: 16,
        top: 20,
        zIndex: 1,
    },
    input: {
        paddingLeft: 48,
        fontSize: 16,
        backgroundColor: '#FAFAFA',
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        marginTop: 8,
        backgroundColor: '#F8F9FF',
        padding: 16,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#E8E5FF',
    },
    checkboxLabel: {
        fontSize: 15,
        color: '#333',
        marginLeft: 12,
        flex: 1,
        fontWeight: '500',
    },
    checkboxIcon: {
        marginLeft: 8,
    },
    modalActions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 24,
        gap: 16,
        borderTopWidth: 1,
        borderTopColor: '#F0F0F0',
        backgroundColor: '#FAFBFF',
    },
    actionButton: {
        flex: 1,
        borderRadius: 12,
        paddingVertical: 4,
    },
    cancelButton: {
        borderColor: '#E0E0E0',
        backgroundColor: 'transparent',
    },
    cancelButtonText: {
        color: '#666',
        fontSize: 16,
        fontWeight: '600',
    },
    saveButton: {
        backgroundColor: '#6B4EFF',
        elevation: 2,
        shadowColor: '#6B4EFF',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
    },
    saveButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '700',
    },
});
