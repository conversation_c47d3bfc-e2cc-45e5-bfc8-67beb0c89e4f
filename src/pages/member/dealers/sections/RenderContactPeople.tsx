/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated, TouchableWithoutFeedback } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import { TextInput, Checkbox, Button, Portal, Modal } from 'react-native-paper';

interface RenderContactPeopleProps {
    createdAt?: string;
    updatedAt?: string;
    copyToClipboard: (text: string, label: string) => void;
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
}

const RenderContactPeople: React.FC<RenderContactPeopleProps> = ({
    createdAt,
    updatedAt,
    copyToClipboard,
    fadeAnim,
    slideAnim
}) => {
    const [modalVisible, setModalVisible] = React.useState(false);
    const [fullName, setFullName] = React.useState('');
    const [position, setPosition] = React.useState('');
    const [phone, setPhone] = React.useState('');
    const [email, setEmail] = React.useState('');
    const [isAuthorized, setIsAuthorized] = React.useState(false);
    const [isSaving, setIsSaving] = React.useState(false);
    
    // Animation values for modal
    const modalScaleAnim = React.useRef(new Animated.Value(0.8)).current;
    const modalOpacityAnim = React.useRef(new Animated.Value(0)).current;

    // Handle modal open with animation
    const openModal = () => {
        setModalVisible(true);
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 1,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start();
    };

    // Handle modal close with animation
    const closeModal = () => {
        Animated.timing(modalOpacityAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
        }).start(() => {
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 100,
                useNativeDriver: true,
            }).start(() => {
                setModalVisible(false);
                // Reset form fields
                setFullName('');
                setPosition('');
                setPhone('');
                setEmail('');
                setIsAuthorized(false);
                setIsSaving(false);
                // Reset animation values for next open
                modalScaleAnim.setValue(0.8);
                modalOpacityAnim.setValue(0);
            });
        });
    };

    const handleSave = async () => {
        // Validate required fields
        if (!fullName || !position || !phone || !email) {
            return;
        }
        
        setIsSaving(true);
        
        // Simulate saving with 2-second delay
        await new Promise((resolve) => setTimeout(() => resolve(null), 2000));
        
        console.log({
            fullName,
            position,
            phone,
            email,
            isAuthorized
        });
        
        // Close modal after saving
        closeModal();
    };

    const handleCancel = () => {
        closeModal();
    };
    
    return (
        <>
            <Animated.View style={[styles.card, {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
            }]}>
                <View style={[styles.cardHeader, { justifyContent: 'space-between', alignItems: 'center' }]}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
                        <Lucide name="users" size={20} color="#6B4EFF" />
                        <Text style={styles.cardTitle}>Kontaklar</Text>
                    </View>
                    <View>
                        <TouchableOpacity style={styles.addButton} onPress={openModal}>
                            <Lucide name="plus" size={14} color="#6B4EFF" />
                            <Text style={styles.addButtonText}>Yeni Kontak</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={styles.cardContent}>
                    {createdAt && (
                        <View style={styles.infoRow}>
                            <Lucide name="calendar-plus" size={16} color="#666" />
                            <Text style={styles.infoLabel}>Oluşturulma:</Text>
                            <TouchableOpacity onLongPress={() => copyToClipboard(
                                new Date(createdAt).toLocaleDateString('tr-TR'),
                                'Oluşturulma tarihi'
                            )}>
                                <Text style={styles.infoValue}>
                                    {new Date(createdAt).toLocaleDateString('tr-TR')}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}
                    {updatedAt && (
                        <View style={styles.infoRow}>
                            <Lucide name="refresh-cw" size={16} color="#666" />
                            <Text style={styles.infoLabel}>Güncelleme:</Text>
                            <TouchableOpacity onLongPress={() => copyToClipboard(
                                new Date(updatedAt).toLocaleDateString('tr-TR'),
                                'Güncelleme tarihi'
                            )}>
                                <Text style={styles.infoValue}>
                                    {new Date(updatedAt).toLocaleDateString('tr-TR')}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </Animated.View>
            
            <Portal>
                <Modal
                    visible={modalVisible}
                    onDismiss={closeModal}
                    contentContainerStyle={{ 
                        width: '100%', 
                        maxWidth: 400, 
                        alignSelf: 'center',
                        backgroundColor: 'transparent'
                    }}
                >
                    <TouchableWithoutFeedback onPress={closeModal}>
                        <View style={styles.modalOverlay}>
                            <TouchableWithoutFeedback>
                                <Animated.View 
                                    style={[
                                        styles.modalContainer, 
                                        {
                                            transform: [{ scale: modalScaleAnim }],
                                            opacity: modalOpacityAnim
                                        }
                                    ]}
                                >
                                    <View style={styles.modalHeader}>
                                        <Text style={styles.modalTitle}>Yeni Kontak Ekle</Text>
                                        <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
                                            <Lucide name="x" size={24} color="#666" />
                                        </TouchableOpacity>
                                    </View>
                                    
                                    <View style={styles.modalContent}>
                                        <View style={styles.inputGroup}>
                                            <Text style={styles.inputLabel}>Tam Adı *</Text>
                                            <View style={styles.inputContainer}>
                                                <Lucide name="user" size={18} color="#6B4EFF" style={styles.inputIcon} />
                                                <TextInput
                                                    value={fullName}
                                                    onChangeText={setFullName}
                                                    mode="outlined"
                                                    placeholder="Ad ve soyad girin"
                                                    style={styles.input}
                                                    theme={{ colors: { primary: '#6B4EFF' } }}
                                                    error={!fullName && fullName !== ''}
                                                    disabled={isSaving}
                                                />
                                            </View>
                                        </View>
                                        
                                        <View style={styles.inputGroup}>
                                            <Text style={styles.inputLabel}>Pozisyon *</Text>
                                            <View style={styles.inputContainer}>
                                                <Lucide name="briefcase" size={18} color="#6B4EFF" style={styles.inputIcon} />
                                                <TextInput
                                                    value={position}
                                                    onChangeText={setPosition}
                                                    mode="outlined"
                                                    placeholder="Pozisyon girin"
                                                    style={styles.input}
                                                    theme={{ colors: { primary: '#6B4EFF' } }}
                                                    error={!position && position !== ''}
                                                    disabled={isSaving}
                                                />
                                            </View>
                                        </View>
                                        
                                        <View style={styles.inputGroup}>
                                            <Text style={styles.inputLabel}>Telefon *</Text>
                                            <View style={styles.inputContainer}>
                                                <Lucide name="phone" size={18} color="#6B4EFF" style={styles.inputIcon} />
                                                <TextInput
                                                    value={phone}
                                                    onChangeText={setPhone}
                                                    mode="outlined"
                                                    placeholder="Telefon numarası girin"
                                                    keyboardType="phone-pad"
                                                    style={styles.input}
                                                    theme={{ colors: { primary: '#6B4EFF' } }}
                                                    error={!phone && phone !== ''}
                                                    disabled={isSaving}
                                                />
                                            </View>
                                        </View>
                                        
                                        <View style={styles.inputGroup}>
                                            <Text style={styles.inputLabel}>E-posta *</Text>
                                            <View style={styles.inputContainer}>
                                                <Lucide name="mail" size={18} color="#6B4EFF" style={styles.inputIcon} />
                                                <TextInput
                                                    value={email}
                                                    onChangeText={setEmail}
                                                    mode="outlined"
                                                    placeholder="E-posta adresi girin"
                                                    keyboardType="email-address"
                                                    style={styles.input}
                                                    theme={{ colors: { primary: '#6B4EFF' } }}
                                                    error={!email && email !== ''}
                                                    disabled={isSaving}
                                                />
                                            </View>
                                        </View>
                                        
                                        <View style={styles.checkboxContainer}>
                                            <Checkbox
                                                status={isAuthorized ? 'checked' : 'unchecked'}
                                                onPress={() => setIsAuthorized(!isAuthorized)}
                                                color="#6B4EFF"
                                                disabled={isSaving}
                                            />
                                            <Text style={styles.checkboxLabel}>Yetkili Kişi</Text>
                                            <Lucide name="shield" size={18} color="#6B4EFF" style={styles.checkboxIcon} />
                                        </View>
                                    </View>
                                    
                                    <View style={styles.modalActions}>
                                        <Button 
                                            mode="outlined" 
                                            onPress={handleCancel} 
                                            style={styles.actionButton}
                                            theme={{ colors: { primary: '#6B4EFF' } }}
                                            disabled={isSaving}
                                        >
                                            İptal
                                        </Button>
                                        <Button 
                                            mode="contained" 
                                            onPress={handleSave} 
                                            style={styles.actionButton}
                                            disabled={!fullName || !position || !phone || !email || isSaving}
                                            theme={{ colors: { primary: '#6B4EFF' } }}
                                            loading={isSaving}
                                        >
                                            {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                                        </Button>
                                    </View>
                                </Animated.View>
                            </TouchableWithoutFeedback>
                        </View>
                    </TouchableWithoutFeedback>
                </Modal>
            </Portal>
        </>
    )
};

export default RenderContactPeople;

const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        color: '#666',
        marginLeft: 12,
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'right',
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 20,
        backgroundColor: '#F0EDFF',
    },
    addButtonText: {
        fontSize: 12,
        color: '#6B4EFF',
        fontWeight: '500',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    modalContainer: {
        width: '100%',
        maxWidth: 400,
        backgroundColor: 'white',
        borderRadius: 16,
        overflow: 'hidden',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 12,
        elevation: 8,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    closeButton: {
        padding: 4,
    },
    modalContent: {
        padding: 20,
    },
    inputGroup: {
        marginBottom: 20,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
        marginBottom: 8,
        marginLeft: 8,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
    },
    inputIcon: {
        position: 'absolute',
        left: 12,
        zIndex: 1,
        top: 18,
    },
    input: {
        flex: 1,
        paddingLeft: 36,
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
        marginTop: 8,
        backgroundColor: '#F8F9FA',
        padding: 12,
        borderRadius: 8,
    },
    checkboxLabel: {
        fontSize: 16,
        color: '#333',
        marginLeft: 8,
        flex: 1,
    },
    checkboxIcon: {
        marginLeft: 8,
    },
    modalActions: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        padding: 20,
        gap: 12,
        borderTopWidth: 1,
        borderTopColor: '#F0F0F0',
    },
    actionButton: {
        minWidth: 100,
    },
});
