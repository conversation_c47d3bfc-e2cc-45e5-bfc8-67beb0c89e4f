import React from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';

interface RenderDokumanlarProps {
    // TODO: Add proper props when implementation is ready
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
}

const RenderDokumanlar: React.FC<RenderDokumanlarProps> = ({ 
    // TODO: Add props when implementation is ready
    fadeAnim,
    slideAnim
}) => {
    return (
        <Animated.View style={[styles.card, { 
            opacity: fadeAnim, 
            transform: [{ translateY: slideAnim }] 
        }]}>
            <View style={styles.cardHeader}>
                <Lucide name="file-text" size={20} color="#6B4EFF" />
                <Text style={styles.cardTitle}>Sözleşme ve Dokümanlar</Text>
            </View>
            <View style={styles.cardContent}>
                {/* Add actual document rendering logic here */}
                <Text style={styles.comingSoon}>Yakında gelecek...</Text>
            </View>
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
        minHeight: 100,
        justifyContent: 'center',
    },
    comingSoon: {
        textAlign: 'center',
        color: '#666',
        fontStyle: 'italic',
    },
});

export default RenderDokumanlar;