import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';

interface LocationInfo {
    sehir?: string;
    ilce?: string;
    adres?: string;
}

interface RenderLocationInfoProps {
    konum?: LocationInfo;
    copyToClipboard: (text: string, label: string) => void;
}

const RenderLocationInfo: React.FC<RenderLocationInfoProps> = ({ konum, copyToClipboard }) => {
    if (!konum) return null;

    return (
        <View style={styles.tabContent}>
            {konum.adres && (
                <View style={styles.infoCol}>
                    <View style={styles.infoRow}>
                        <Lucide name="navigation" size={16} color="#666" />
                        <TouchableOpacity onLongPress={() => copyToClipboard(konum.adres!, 'Adres')}>
                            <Text style={styles.infoValue_t}>{konum.adres}</Text>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.subInfoRow}>
                        {konum.ilce && (
                            <TouchableOpacity onLongPress={() => copyToClipboard(konum.ilce!, 'İlçe')}>
                                <Text style={styles.infoValue}>{konum.ilce}</Text>
                            </TouchableOpacity>
                        )}
                        <Text>&nbsp;&nbsp;</Text>
                        {konum.sehir && (
                            <TouchableOpacity onLongPress={() => copyToClipboard(konum.sehir!, 'Şehir')}>
                                <Text style={styles.infoValue}>{konum.sehir}</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    tabContent: {
        paddingVertical: 8,
    },
    infoCol: {
        marginHorizontal: 0,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    subInfoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 22,
        marginBottom: 12,
    },
    infoValue_t: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'left',
        marginLeft: 5,
    },
    infoValue: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        textAlign: 'right',
    },
});

export default RenderLocationInfo;