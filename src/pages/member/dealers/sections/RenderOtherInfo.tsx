import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';

interface OtherInfo {
    vd?: string;
    vdNo?: string;
    tursabNo?: string;
    bankaIban?: string;
    banka?: string;
}

interface BackofficeInfo {
    agent_bo_code?: string;
    account_code?: string;
}

interface RenderOtherInfoProps {
    diger?: OtherInfo;
    backoffice?: BackofficeInfo;
    timestamps?: {
        createdAt?: string;
        updatedAt?: string;
    };
    copyToClipboard: (text: string, label: string) => void;
}

const RenderOtherInfo: React.FC<RenderOtherInfoProps> = ({ 
    diger, 
    backoffice, 
    timestamps,
    copyToClipboard 
}) => {
    if (!diger && !backoffice) return null;

    return (
        <View style={[styles.tabContent, { borderWidth: 0, paddingBottom: 0 }]}>
            {diger?.vd && (
                <View style={styles.infoRow}>
                    <Lucide name="building" size={16} color="#666" />
                    <Text style={styles.infoLabel}>Vergi Dairesi:</Text>
                    <TouchableOpacity onLongPress={() => copyToClipboard(diger.vd!, 'Vergi dairesi')}>
                        <Text style={styles.infoValue}>{diger.vd}</Text>
                    </TouchableOpacity>
                    <Text>&nbsp;&nbsp;</Text>
                    {diger.vdNo && (
                        <TouchableOpacity onLongPress={() => copyToClipboard(diger.vdNo!, 'Vergi no')}>
                            <Text style={styles.infoValue}>{diger.vdNo}</Text>
                        </TouchableOpacity>
                    )}
                </View>
            )}
            
            {diger?.tursabNo && (
                <View style={styles.infoRow}>
                    <Lucide name="award" size={16} color="#666" />
                    <Text style={styles.infoLabel}>Tursab No:</Text>
                    <TouchableOpacity onLongPress={() => copyToClipboard(diger.tursabNo!, 'Tursab no')}>
                        <Text style={styles.infoValue}>{diger.tursabNo}</Text>
                    </TouchableOpacity>
                </View>
            )}

            {diger?.banka && (
                <View style={styles.infoRow}>
                    <Lucide name="banknote" size={16} color="#666" />
                    <Text style={styles.infoLabel}>Banka:</Text>
                    <TouchableOpacity onLongPress={() => copyToClipboard(diger.banka!, 'Banka')}>
                        <Text style={styles.infoValue}>{diger.banka}</Text>
                    </TouchableOpacity>
                </View>
            )}

            {diger?.bankaIban && (
                <View style={styles.infoRow}>
                    <Lucide name="wallet" size={16} color="#666" />
                    <Text style={styles.infoLabel}>IBAN:</Text>
                    <TouchableOpacity onLongPress={() => copyToClipboard(diger.bankaIban!, 'IBAN')}>
                        <Text style={styles.infoValue}>{diger.bankaIban}</Text>
                    </TouchableOpacity>
                </View>
            )}

            {backoffice && (
                <>
                    {backoffice.agent_bo_code && (
                        <View style={styles.infoRow}>
                            <Lucide name="hash" size={16} color="#666" />
                            <Text style={styles.infoLabel}>Acente BO Kodu:</Text>
                            <TouchableOpacity onLongPress={() => copyToClipboard(backoffice.agent_bo_code!, 'Agent BO kodu')}>
                                <Text style={styles.infoValue}>{backoffice.agent_bo_code}</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                    {backoffice.account_code && (
                        <View style={styles.infoRow}>
                            <Lucide name="hash" size={16} color="#666" />
                            <Text style={styles.infoLabel}>Hesap Kodu:</Text>
                            <TouchableOpacity onLongPress={() => copyToClipboard(backoffice.account_code!, 'Hesap kodu')}>
                                <Text style={styles.infoValue}>{backoffice.account_code}</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </>
            )}

            <View style={styles.timestampsContainer}>
                {timestamps?.createdAt && (
                    <View style={styles.timestampRow}>
                        <Lucide name="plus" size={16} color="#aaa" />
                        <TouchableOpacity onLongPress={() => copyToClipboard(
                            new Date(timestamps.createdAt!).toLocaleDateString('tr-TR'), 
                            'Oluşturulma tarihi'
                        )}>
                            <Text style={styles.timestampText}>
                                {new Date(timestamps.createdAt!).toLocaleDateString('tr-TR')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                )}
                {timestamps?.updatedAt && (
                    <View style={styles.timestampRow}>
                        <Lucide name="refresh-cw" size={16} color="#aaa" />
                        <TouchableOpacity onLongPress={() => copyToClipboard(
                            new Date(timestamps.updatedAt!).toLocaleDateString('tr-TR'), 
                            'Güncelleme tarihi'
                        )}>
                            <Text style={styles.timestampText}>
                                {new Date(timestamps.updatedAt!).toLocaleDateString('tr-TR')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    tabContent: {
        paddingVertical: 8,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        color: '#666',
        marginLeft: 12,
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'right',
    },
    timestampsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 12,
        marginTop: 0,
        borderTopWidth: 0.5,
        borderTopColor: '#eee',
        paddingTop: 8
    },
    timestampRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 0
    },
    timestampText: {
        fontSize: 12,
        color: '#ccc',
        fontWeight: '500',
        textAlign: 'right',
        marginLeft: 8
    }
});

export default RenderOtherInfo;